package config

import (
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/precize/logger"
	"gopkg.in/yaml.v3"
)

type ElasticsearchIdentityConfig struct {
	BatchSize int `yaml:"batchSize"`
}

type ElasticsearchConfig struct {
	Host                 string `yaml:"host"`
	Port                 int    `yaml:"port"`
	Scheme               string `yaml:"scheme"`
	Username             string `yaml:"username"`
	Password             string `yaml:"password"`
	DefaultLastEventTime string `yaml:"default_last_event_time"`
	CACertPath           string `yaml:"caCertPath"`
}

type SpringConfig struct {
	Elasticsearch ElasticsearchConfig `yaml:"elasticsearch"`
}

type Config struct {
	Spring   SpringConfig `yaml:"spring"`
	Analyzer struct {
		FetchInterval string `yaml:"fetch_interval"`
	} `yaml:"analyzer"`
}

var (
	config     Config
	configLock sync.RWMutex
)

// LoadConfig loads the configuration from the specified YAML file path
//
// Example usage:
//
//	if err := config.LoadConfig("./config/application.yml"); err != nil {
//		log.Fatalf("Error loading config: %s", err)
//	}
func LoadConfig(filepath string) error {
	configLock.Lock()
	defer configLock.Unlock()

	data, err := os.ReadFile(filepath)
	if err != nil {
		config.Spring.Elasticsearch.Host = "localhost"
		config.Spring.Elasticsearch.Port = 9200
		config.Spring.Elasticsearch.Scheme = "http"
		return err
	}

	if err := yaml.Unmarshal(data, &config); err != nil {
		return err
	}
	logger.Print(logger.INFO, fmt.Sprintf("Config loaded from %s", filepath))
	return nil
}

// GetConfig returns the current configuration
func GetConfig() Config {
	configLock.RLock()
	defer configLock.RUnlock()
	return config
}

// GetFetchInterval returns the fetch interval
func GetFetchInterval() time.Duration {
	cfg := GetConfig()

	fetch_interval := 48 * time.Hour

	if cfg.Analyzer.FetchInterval != "" {
		cfgInterval, err := time.ParseDuration(cfg.Analyzer.FetchInterval)
		if err != nil {
			logger.Print(logger.INFO, fmt.Sprintf("Error parsing interval: %s", err))
		} else {
			fetch_interval = cfgInterval
		}
	}

	return fetch_interval
}

// GetElasticsearchConfig returns the Elasticsearch configuration
func GetElasticsearchConfig() ElasticsearchConfig {
	return GetConfig().Spring.Elasticsearch
}
