package config

import (
	"fmt"

	"github.com/fsnotify/fsnotify"
	"github.com/precize/logger"
)

// WatchConfigFile watches the config file for changes and calls the provided function when it changes.
//
//	func main() {
//		// Load the initial config
//		if err := config.LoadConfig(); err != nil {
//			log.Fatalf("Error loading config: %v", err)
//		}
//
//		// Watch the config file for changes
//		go config.WatchConfigFile(func() {
//			logger.Print(logger.INFO, fmt.Sprintf("Config reloaded"))
//		})
//
//		// Run the main program
//		// ...
//	}
func WatchConfigFile(configPath string, f func()) {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("%+v", err))
		return
	}
	defer watcher.Close()

	err = watcher.Add(configPath)
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("%+v", err))
		return
	}

	for {
		select {
		case event, ok := <-watcher.Events:
			if !ok {
				return
			}
			if event.Op&fsnotify.Write == fsnotify.Write {
				logger.Print(logger.INFO, fmt.Sprintf("Config file changed, reloading..."))
				if err := LoadConfig(configPath); err != nil {
					logger.Print(logger.INFO, fmt.Sprintf("Error reloading config: %v", err))
					continue
				}
				f()
			}
		case err, ok := <-watcher.Errors:
			if !ok {
				return
			}
			logger.Print(logger.INFO, fmt.Sprintf("Watcher error: %v", err))
		}
	}
}
