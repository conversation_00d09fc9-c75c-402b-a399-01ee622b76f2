package elasticsearch

import (
	"fmt"
	"regexp"
	"slices"
	"strings"
	"time"

	"github.com/precize/analyzer/internal/esjson"
	"github.com/precize/analyzer/internal/utils"
	"github.com/precize/logger"
)

var lastestEntityBehaviour map[string][]*EntityBehaviour

type ActivityOverTime struct {
	Date  string `json:"date"`
	Count int    `json:"count"`
}

type EntityBehaviour struct {
	ID               string             `json:"id,omitempty"`
	AccountIDs       []string           `json:"accountIds,omitempty"`
	TenantID         string             `json:"tenantId,omitempty"`
	EntityType       string             `json:"entityType,omitempty"`
	ActivityOverTime []ActivityOverTime `json:"activityOverTime,omitempty"`
	EntityName       string             `json:"entityName,omitempty"`
	ResourceTypes    []string           `json:"resourceTypes,omitempty"`
	EventSources     []string           `json:"eventSources,omitempty"`
	SourceIPs        []string           `json:"sourceIps,omitempty"`
	SourceApps       []string           `json:"sourceApps,omitempty"`
	Regions          []string           `json:"regions,omitempty"`
	CreatedAt        string             `json:"createdAt,omitempty"`
	UpdatedAt        string             `json:"updatedAt,omitempty"`
	EventNames       []string           `json:"eventNames,omitempty"`
	Count            int                `json:"count,omitempty"`
	TotalEvents      int                `json:"totalEvents,omitempty"`
}

type EntityBehaviourAggregationResponse struct {
	Hits struct {
		Total struct {
			Value    int    `json:"value"`
			Relation string `json:"relation"`
		} `json:"total"`
	} `json:"hits"`
	Aggregations struct {
		AccessKeys TermsAggregation `json:"access_keys"`
		Users      TermsAggregation `json:"users"`
	} `json:"aggregations"`
}

type TermsAggregation struct {
	DocCountErrorUpperBound int      `json:"doc_count_error_upper_bound"`
	SumOtherDocCount        int      `json:"sum_other_doc_count"`
	Buckets                 []Bucket `json:"buckets"`
}

type Bucket struct {
	Key              string                    `json:"key"`
	DocCount         int                       `json:"doc_count"`
	AccountIds       *TermsAggregation         `json:"accountIds,omitempty"`
	Regions          *TermsAggregation         `json:"regions,omitempty"`
	EventNames       *TermsAggregation         `json:"event_names,omitempty"`
	EventSources     *TermsAggregation         `json:"event_sources,omitempty"`
	SourceApps       *TermsAggregation         `json:"source_apps,omitempty"`
	SourceIPs        *TermsAggregation         `json:"source_ips,omitempty"`
	ResourceTypes    *TermsAggregation         `json:"resource_types,omitempty"`
	ActivityOverTime *DateHistogramAggregation `json:"activity_over_time,omitempty"`
}

type DateHistogramAggregation struct {
	Buckets []DateHistogramBucket `json:"buckets"`
}

type DateHistogramBucket struct {
	KeyAsString string `json:"key_as_string"`
	Key         int64  `json:"key"`
	DocCount    int    `json:"doc_count"`
}

func (es *Client) AggregateEntityBehaviour(tid string) ([]*EntityBehaviour, error) {
	now := time.Now().UTC().Format(utils.ESTimeLayout)
	queryString, err := buildAggregationQuery(tid)
	if err != nil {
		return nil, err
	}

	response, err := ExecuteQuery[EntityBehaviourAggregationResponse](es, CLOUD_ACTIVITY_INDEX, queryString)
	if err != nil {
		return nil, err
	}

	var entityBehaviours []*EntityBehaviour

	for _, bucket := range response.Aggregations.AccessKeys.Buckets {
		var accountIds []string
		for _, accountBucket := range bucket.AccountIds.Buckets {
			if accountBucket.Key == "" {
				continue
			}
			if slices.Contains(accountIds, accountBucket.Key) {
				continue
			}
			accountIds = append(accountIds, accountBucket.Key)
		}

		var sourceIps []string
		for _, sourceIpBucket := range bucket.SourceIPs.Buckets {
			if sourceIpBucket.Key == "" {
				continue
			}
			if slices.Contains(sourceIps, sourceIpBucket.Key) {
				continue
			}
			sourceIps = append(sourceIps, sourceIpBucket.Key)
		}

		docID := utils.GenerateCombinedHashID(tid, "access_key", bucket.Key)

		enityBehaviour := &EntityBehaviour{
			ID:         docID,
			TenantID:   tid,
			EntityType: "access_key",
			EntityName: bucket.Key,
			AccountIDs: accountIds,
			SourceIPs:  sourceIps,
			CreatedAt:  now,
			UpdatedAt:  now,
		}
		entityBehaviours = append(entityBehaviours, enityBehaviour)
	}

	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	for _, bucket := range response.Aggregations.Users.Buckets {
		var accountIds = make(map[string]struct{})
		for _, accountBucket := range bucket.AccountIds.Buckets {
			if accountBucket.Key == "" {
				continue
			}
			if _, ok := accountIds[accountBucket.Key]; !ok {
				accountIds[accountBucket.Key] = struct{}{}
			}
		}

		var sourceIps = make(map[string]struct{})
		for _, sourceIpBucket := range bucket.SourceIPs.Buckets {
			if sourceIpBucket.Key == "" {
				continue
			}
			if _, ok := sourceIps[sourceIpBucket.Key]; !ok {
				sourceIps[sourceIpBucket.Key] = struct{}{}
			}
		}

		var sourceApps = make(map[string]struct{})
		for _, sourceAppBucket := range bucket.SourceApps.Buckets {
			if sourceAppBucket.Key == "" {
				continue
			}
			if _, ok := sourceApps[sourceAppBucket.Key]; !ok {
				sourceApps[sourceAppBucket.Key] = struct{}{}
			}
		}

		var regions = make(map[string]struct{})
		for _, regionBucket := range bucket.Regions.Buckets {
			if regionBucket.Key == "" {
				continue
			}
			if _, ok := regions[regionBucket.Key]; !ok {
				regions[regionBucket.Key] = struct{}{}
			}
		}

		var eventNames = make(map[string]struct{})
		for _, eventNameBucket := range bucket.EventNames.Buckets {
			if eventNameBucket.Key == "" {
				continue
			}
			if _, ok := eventNames[eventNameBucket.Key]; !ok {
				eventNames[eventNameBucket.Key] = struct{}{}
			}
		}

		var eventSources = make(map[string]struct{})
		for _, eventSourceBucket := range bucket.EventSources.Buckets {
			if eventSourceBucket.Key == "" {
				continue
			}
			if _, ok := eventSources[eventSourceBucket.Key]; !ok {
				eventSources[eventSourceBucket.Key] = struct{}{}
			}
		}

		var resourceTypes = make(map[string]struct{})
		for _, resourceTypeBucket := range bucket.ResourceTypes.Buckets {
			if resourceTypeBucket.Key == "" {
				continue
			}
			if _, ok := resourceTypes[resourceTypeBucket.Key]; !ok {
				resourceTypes[resourceTypeBucket.Key] = struct{}{}
			}
		}

		var activityOverTime []ActivityOverTime
		for _, activityBucket := range bucket.ActivityOverTime.Buckets {
			activityOverTime = append(activityOverTime, ActivityOverTime{
				Date:  time.Unix(activityBucket.Key/1000, 0).UTC().Format(utils.ESTimeLayout),
				Count: activityBucket.DocCount,
			})
		}

		docID := utils.GenerateCombinedHashID(tid, "entity", bucket.Key)

		entityType := "entity"
		if strings.Contains(bucket.Key, "@") {
			matched := emailRegex.MatchString(bucket.Key)
			if matched {
				if strings.HasSuffix(bucket.Key, ".gserviceaccount.com") {
					entityType = "gcp_service_account"
				} else {
					entityType = "user"
				}
			}
		}

		accountIdsKeys := []string{}
		for accountId := range accountIds {
			accountIdsKeys = append(accountIdsKeys, accountId)
		}

		sourceIpsKeys := []string{}
		for sourceIp := range sourceIps {
			sourceIpsKeys = append(sourceIpsKeys, sourceIp)
		}

		sourceAppsKeys := []string{}
		for sourceApp := range sourceApps {
			sourceAppsKeys = append(sourceAppsKeys, sourceApp)
		}

		regionsKeys := []string{}
		for region := range regions {
			regionsKeys = append(regionsKeys, region)
		}

		eventNamesKeys := []string{}
		for eventName := range eventNames {
			eventNamesKeys = append(eventNamesKeys, eventName)
		}

		eventSourcesKeys := []string{}
		for eventSource := range eventSources {
			eventSourcesKeys = append(eventSourcesKeys, eventSource)
		}

		resourceTypesKeys := []string{}
		for resourceType := range resourceTypes {
			resourceTypesKeys = append(resourceTypesKeys, resourceType)
		}

		entityBehaviour := &EntityBehaviour{
			ID:               docID,
			TenantID:         tid,
			EntityType:       entityType,
			EntityName:       bucket.Key,
			AccountIDs:       accountIdsKeys,
			SourceIPs:        sourceIpsKeys,
			SourceApps:       sourceAppsKeys,
			Regions:          regionsKeys,
			EventNames:       eventNamesKeys,
			EventSources:     eventSourcesKeys,
			ResourceTypes:    resourceTypesKeys,
			ActivityOverTime: activityOverTime,
			CreatedAt:        now,
			UpdatedAt:        now,
		}

		entityBehaviours = append(entityBehaviours, entityBehaviour)
	}

	if lastestEntityBehaviour == nil {
		lastestEntityBehaviour = make(map[string][]*EntityBehaviour)
	}

	SetEntityBehaviour(tid, entityBehaviours)
	return entityBehaviours, nil
}

func buildAggregationQuery(tid string) (string, error) {
	params := esjson.QueryParams{
		StartTime: time.Now().Add(-7 * 24 * time.Hour).UTC().Format(utils.ESTimeLayout),
		EndTime:   time.Now().UTC().Format(utils.ESTimeLayout),
		TenantId:  tid,
	}
	return esjson.LoadQuery(esjson.ENTITY_BEHAVIOUR_AGGREGATION, &params)
}

func GetEntityBehaviour(tid string) []*EntityBehaviour {
	if _, ok := lastestEntityBehaviour[tid]; !ok {
		return nil
	}
	return lastestEntityBehaviour[tid]
}

func GetEntityBehaviourByUsername(tid string, username string) *EntityBehaviour {
	if _, ok := lastestEntityBehaviour[tid]; !ok {
		return nil
	}
	for _, behaviour := range lastestEntityBehaviour[tid] {
		if behaviour.EntityName == username {
			return behaviour
		}
	}
	return nil
}

func UpdateEntityBehaviourByUsername(es *Client, tid string, username string, behaviour *EntityBehaviour) error {
	logger.Print(logger.INFO, "Updating entity behaviour for username:", username)
	logger.Print(logger.INFO, "Behaviour:", behaviour)
	if _, ok := lastestEntityBehaviour[tid]; !ok {
		return fmt.Errorf("entity behaviour not found")
	}
	for i, b := range lastestEntityBehaviour[tid] {
		if b.EntityName == username {
			lastestEntityBehaviour[tid][i] = behaviour
			return nil
		}
	}
	return fmt.Errorf("entity behaviour not found")
}

type FetchEntityBehaviourResponse struct {
	Hits struct {
		Total struct {
			Value    int    `json:"value"`
			Relation string `json:"relation"`
		} `json:"total"`
		Hits []struct {
			Source EntityBehaviour `json:"_source"`
		} `json:"hits"`
	} `json:"hits"`
}

func FetchEntityBehaviour(es *Client, tid string, username string, startTime, endTime time.Time) ([]*EntityBehaviour, error) {
	params := esjson.QueryParams{
		StartTime: startTime.UTC().Format(utils.ESTimeLayout),
		EndTime:   endTime.UTC().Format(utils.ESTimeLayout),
		TenantId:  tid,
		Username:  username,
	}
	queryString, err := esjson.LoadQuery(esjson.FETCH_ENTITY_BEHAVIOUR, &params)
	if err != nil {
		return nil, err
	}

	response, err := ExecuteQuery[FetchEntityBehaviourResponse](es, ENTITY_BEHAVIOUR_INDEX, queryString)
	if err != nil {
		return nil, err
	}

	if response.Hits.Total.Value == 0 {
		return nil, nil
	}

	entityBehaviours := make([]*EntityBehaviour, len(response.Hits.Hits))
	for i, hit := range response.Hits.Hits {
		entityBehaviours[i] = &hit.Source
	}

	return entityBehaviours, nil
}

func SetEntityBehaviour(tid string, ebs []*EntityBehaviour) {
	if len(ebs) == 0 {
		return
	}
	if lastestEntityBehaviour == nil {
		lastestEntityBehaviour = make(map[string][]*EntityBehaviour)
	}
	lastestEntityBehaviour[tid] = ebs
}

func FetchEntityBehaviourByTenant(es *Client, tid string, startTime, endTime time.Time) ([]*EntityBehaviour, error) {
	params := esjson.QueryParams{
		StartTime: startTime.UTC().Format(utils.ESTimeLayout),
		EndTime:   endTime.UTC().Format(utils.ESTimeLayout),
		TenantId:  tid,
	}
	queryString, err := esjson.LoadQuery(esjson.GET_DATA_BY_TENANT_ID, &params)
	if err != nil {
		return nil, err
	}

	response, err := ExecuteQuery[FetchEntityBehaviourResponse](es, ENTITY_BEHAVIOUR_INDEX, queryString)
	if err != nil {
		return nil, err
	}

	if response.Hits.Total.Value == 0 {
		return nil, nil
	}

	entityBehaviours := make([]*EntityBehaviour, len(response.Hits.Hits))
	for i, hit := range response.Hits.Hits {
		entityBehaviours[i] = &hit.Source
	}

	return entityBehaviours, nil
}
