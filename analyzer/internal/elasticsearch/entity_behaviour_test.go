package elasticsearch

import (
	"testing"
	"time"

	"github.com/precize/analyzer/config"
)

func TestEntityBehaviour(t *testing.T) {
	config.LoadConfig("")
	// if err != nil {
	// 	t.<PERSON>rrorf("Error loading config: %s", err)
	// }
	client := NewClient()
	if _, err := client.Ping(); err != nil {
		t.<PERSON>rrorf("Error pinging elasticsearch: %s", err)
		return
	}
	tenantId := "jZDOJpQBUMY_FFmv9GrV"
	username := "<EMAIL>"
	startTime := time.Now().Add(-time.Hour * 24 * 30 * 2)
	endTime := time.Now()
	entityBehaviours, err := FetchEntityBehaviour(client, tenantId, username, startTime, endTime)
	if err != nil {
		t.Errorf("Error fetching entity behaviour: %s", err)
		return
	}

	if entityBehaviours == nil {
		t.Errorf("Entity behaviours is nil")
		return
	}

	if len(entityBehaviours) == 0 {
		t.<PERSON><PERSON><PERSON>("Entity behaviours is empty")
		return
	}
}
