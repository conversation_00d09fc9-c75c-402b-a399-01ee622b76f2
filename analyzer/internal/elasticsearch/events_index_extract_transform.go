package elasticsearch

import (
	"fmt"
	"slices"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	esjson "github.com/precize/analyzer/internal/esjson"
	"github.com/precize/analyzer/internal/utils"
	"github.com/precize/logger"
)

type EventsAggregationResponse struct {
	Took     int  `json:"took"`
	TimedOut bool `json:"timed_out"`
	Shards   struct {
		Total      int `json:"total"`
		Successful int `json:"successful"`
		Skipped    int `json:"skipped"`
		Failed     int `json:"failed"`
	} `json:"_shards"`
	Hits struct {
		Total struct {
			Value    int    `json:"value"`
			Relation string `json:"relation"`
		} `json:"total"`
	} `json:"hits"`
	Aggregations struct {
		GroupByResourceName struct {
			Buckets []GroupByResourceNameBucket `json:"buckets"`
		} `json:"groubbyresourcename"`
	} `json:"aggregations"`
}

type GroupByResourceNameBucket struct {
	Key             string `json:"key"`
	DocCount        int    `json:"doc_count"`
	GroupbyUsername struct {
		Buckets []GroupByUsernameBucket `json:"buckets"`
	} `json:"groupbyusername"`
}

type GroupByUsernameBucket struct {
	Key              string `json:"key"`
	DocCount         int    `json:"doc_count"`
	GroupbyEventName struct {
		Buckets []GroupByEventNameBucket `json:"buckets"`
	} `json:"groupbyeventname"`
}

type GroupByEventNameBucket struct {
	Key              string `json:"key"`
	DocCount         int    `json:"doc_count"`
	GroupBySourceApp struct {
		Buckets []GroupBySourceAppBucket `json:"buckets"`
	} `json:"groupbysourceapp"`
}

type GroupBySourceAppBucket struct {
	Key             string `json:"key"`
	DocCount        int    `json:"doc_count"`
	GroupBySourceIP struct {
		Buckets []GroupBySourceIPBucket `json:"buckets"`
	} `json:"groupbysourceip"`
}

type GroupBySourceIPBucket struct {
	Key        string     `json:"key"`
	DocCount   int        `json:"doc_count"`
	MatchedDoc MatchedDoc `json:"matchedDoc"`
}

type MatchedDoc struct {
	Hits struct {
		Total struct {
			Value    int    `json:"value"`
			Relation string `json:"relation"`
		} `json:"total"`
		MaxScore float64 `json:"max_score"`
		Hits     []struct {
			Index  string  `json:"_index"`
			Type   string  `json:"_type"`
			ID     string  `json:"_id"`
			Score  float64 `json:"_score"`
			Source Source  `json:"_source"`
		} `json:"hits"`
	} `json:"hits"`
}

type Source struct {
	EventSource                 string `json:"eventSource,omitempty"`
	TenantId                    string `json:"tenantId,omitempty"`
	AccountId                   string `json:"accountId,omitempty"`
	EventName                   string `json:"eventName,omitempty"`
	EventStatus                 string `json:"eventStatus,omitempty"`
	SourceIp                    string `json:"sourceIp,omitempty"`
	SourceApp                   string `json:"sourceApp,omitempty"`
	ServiceCode                 string `json:"serviceCode,omitempty"`
	EventTime                   string `json:"eventTime,omitempty"`
	CloudTrailEvent             string `json:"cloudTrailEvent,omitempty"`
	AccessKeyPrincipalAccountID string `json:"accessKeyPrincipalAccountId,omitempty"`
	AccessKeyPrincipalID        string `json:"accessKeyPrincipalId,omitempty"`
	AccessKeyPrincipalType      string `json:"accessKeyPrincipalType,omitempty"`
	AccessKeyID                 string `json:"accessKeyId,omitempty"`
	Region                      string `json:"region,omitempty"`
	Resources                   []struct {
		ResourceType string `json:"resourceType,omitempty"`
	} `json:"resources,omitempty"`
}

func (es *Client) FetchAggregatedData(tid, lastEventTime string) ([]Event, string, error) {
	now := time.Now().UTC().Format(utils.ESTimeLayout)

	var events sync.Map
	var maxEventTime atomic.Value
	maxEventTime.Store(lastEventTime)

	const maxWorkers = 10
	workerPool := make(chan struct{}, maxWorkers)

	currentStartTime := lastEventTime

	for {
		queryString, err := buildQueryString(tid, currentStartTime, now)
		if err != nil {
			return nil, lastEventTime, fmt.Errorf("failed to load query: %w", err)
		}

		response, err := ExecuteQuery[EventsAggregationResponse](es, CLOUD_ACTIVITY_INDEX, queryString)
		if err != nil {
			return nil, lastEventTime, fmt.Errorf("failed to execute query: %w", err)
		}

		var batchMaxTime string
		batchMaxTime = currentStartTime

		var wg sync.WaitGroup
		for _, event := range response.Aggregations.GroupByResourceName.Buckets {
			resourceName := event.Key
			workerPool <- struct{}{}
			wg.Add(1)
			go func(event GroupByResourceNameBucket, resourceName string) {
				defer func() {
					wg.Done()
					<-workerPool
				}()
				for _, usernameBucket := range event.GroupbyUsername.Buckets {
					username := usernameBucket.Key
					for _, eventNameBucket := range usernameBucket.GroupbyEventName.Buckets {
						if isIgnoredEvent(eventNameBucket.Key) {
							continue
						}
						for _, sourceIpBucket := range eventNameBucket.GroupBySourceApp.Buckets {
							for _, sourceAppBucket := range sourceIpBucket.GroupBySourceIP.Buckets {
								doc_count := sourceAppBucket.MatchedDoc.Hits.Total.Value
								for _, doc := range sourceAppBucket.MatchedDoc.Hits.Hits {
									doc_id := doc.ID
									source := doc.Source

									errorCount := 0
									if source.EventStatus == EVENT_ERROR_STATUS_FAILURE {
										errorCount++
									}

									sourceEventTime, err := time.Parse(utils.ESTimeLayout, source.EventTime)
									if err != nil {
										logger.Print(logger.INFO, fmt.Sprintf("Error parsing event time for doc %s: %s", doc.ID, err))
										continue
									}

									currentMaxTime, err := time.Parse(utils.ESTimeLayout, batchMaxTime)
									if err != nil {
										logger.Print(logger.INFO, fmt.Sprintf("Error parsing batch max time: %s", err))
										continue
									}

									if sourceEventTime.After(currentMaxTime) {
										batchMaxTime = source.EventTime
									}

									docID := utils.GenerateCombinedHashID(source.AccountId, source.TenantId, username, resourceName)
									value, ok := events.Load(docID)

									if !ok {
										sourceIp := []string{}
										if source.SourceIp != "" {
											// not working as expected
											// cidrIP, err := utils.IPToCIDR(source.SourceIp)
											// if err != nil {
											// 	sourceIp = append(sourceIp, source.SourceIp)
											// } else {
											// 	sourceIp = append(sourceIp, cidrIP)
											// }
											sourceIp = append(sourceIp, source.SourceIp)
										}

										resourceType := ""
										if len(source.Resources) > 0 {
											resourceType = source.Resources[0].ResourceType
										}

										// TODO: Need to figure something similar for AWS and Azure
										sourceApp := []string{}
										if source.SourceApp != "" {
											if strings.HasPrefix(source.SourceApp, "GCE Managed Instance Group") &&
												strings.Contains(source.EventName, "compute.instances.") &&
												(resourceType == "VMInstance" || resourceType == "") {
												continue
											} else if strings.Contains(source.SourceApp, "GCE CSI Driver") &&
												strings.Contains(source.EventName, "compute.disks.") {
												continue
											}
											sourceApp = append(sourceApp, source.SourceApp)
										}

										assumedRole := []string{}
										if len(source.AccessKeyPrincipalID) > 0 {
											assumedRole = append(assumedRole, username)
											username = source.AccessKeyPrincipalID
										}

										additionalInfo := make(map[string]any)
										if len(source.AccessKeyPrincipalAccountID) > 0 {
											additionalInfo["accessKeyPrincipalAccountId"] = source.AccessKeyPrincipalAccountID
										}
										if len(source.AccessKeyPrincipalType) > 0 {
											additionalInfo["accessKeyPrincipalType"] = source.AccessKeyPrincipalType
										}

										if len(source.AccessKeyID) > 0 {
											additionalInfo["accessKeyId"] = source.AccessKeyID
										}

										newEvent := Event{
											ID:               docID,
											AccountID:        source.AccountId,
											TenantID:         source.TenantId,
											OriginalUsername: usernameBucket.Key,
											Username:         username,
											ResourceName:     resourceName,
											EventSource:      []string{source.EventSource},
											SourceIp:         sourceIp,
											SourceApp:        sourceApp,
											ServiceCode:      source.ServiceCode,
											CreatedAt:        now,
											LastUpdatedAt:    now,
											EventName:        []string{source.EventName},
											ErrorCount:       errorCount,
											ResourceType:     resourceType,
											Count:            doc_count,
											AssumeRole:       assumedRole,
											Additional:       additionalInfo,
											Region:           []string{source.Region},
											DocIDs:           []string{doc_id},
										}

										events.Store(docID, newEvent)
									} else {
										e := value.(Event)

										if errorCount > 0 {
											e.ErrorCount += 1
										}

										if source.EventName != "" {
											if !slices.Contains(e.EventName, source.EventName) {
												e.EventName = append(e.EventName, source.EventName)
												if len(e.EventName) > 10 {
													e.EventName = e.EventName[len(e.EventName)-10:]
												}
											}
										}

										// not working as expected
										// var sourceIp string
										// if source.SourceIp != "" {
										// 	cidrIP, err := utils.IPToCIDR(source.SourceIp)
										// 	if err != nil {
										// 		sourceIp = source.SourceIp
										// 	} else {
										// 		sourceIp = cidrIP
										// 	}
										// }
										if source.SourceIp != "" && !slices.Contains(e.SourceIp, source.SourceIp) {
											e.SourceIp = append(e.SourceIp, source.SourceIp)
										}
										if source.EventSource != "" && !slices.Contains(e.EventSource, source.EventSource) {
											e.EventSource = append(e.EventSource, source.EventSource)
										}
										if source.SourceApp != "" && !slices.Contains(e.SourceApp, source.SourceApp) {
											e.SourceApp = append(e.SourceApp, source.SourceApp)
										}

										if source.Region != "" && !slices.Contains(e.Region, source.Region) {
											e.Region = append(e.Region, source.Region)
										}

										if source.AccessKeyPrincipalID != "" {
											if !slices.Contains(e.AssumeRole, username) {
												e.AssumeRole = append(e.AssumeRole, username)
											}
											e.Username = source.AccessKeyPrincipalID
										}

										if !slices.Contains(e.DocIDs, doc_id) {
											e.DocIDs = append(e.DocIDs, doc_id)
										}

										if e.Additional == nil {
											e.Additional = make(map[string]any)
										}
										additionalInfo := e.Additional
										if len(source.AccessKeyPrincipalAccountID) > 0 {
											additionalInfo["accessKeyPrincipalAccountId"] = source.AccessKeyPrincipalAccountID
										}
										if len(source.AccessKeyPrincipalType) > 0 {
											additionalInfo["accessKeyPrincipalType"] = source.AccessKeyPrincipalType
										}
										if len(source.AccessKeyID) > 0 {
											additionalInfo["accessKeyId"] = source.AccessKeyID
										}

										e.Count += doc_count

										e.LastUpdatedAt = source.EventTime
										events.Store(docID, e)
									}

									maxEventTimeParsed, err := time.Parse(utils.ESTimeLayout, maxEventTime.Load().(string))
									if err != nil {
										logger.Print(logger.INFO, fmt.Sprintf("Error parsing max event time for doc %s: %s", docID, err))
										continue
									}

									if sourceEventTime.After(maxEventTimeParsed) {
										maxEventTime.Store(source.EventTime)
									}
								}
							}
						}
					}
				}
			}(event, resourceName)
		}
		wg.Wait()

		if response.Hits.Total.Value == 0 || batchMaxTime == currentStartTime {
			break
		}

		currentStartTime = batchMaxTime
	}

	var result []Event
	events.Range(func(key, value any) bool {
		result = append(result, value.(Event))
		return true
	})

	return result, maxEventTime.Load().(string), nil
}

func buildQueryString(tid, startTime, endTime string) (string, error) {
	params := esjson.QueryParams{
		StartTime: startTime,
		EndTime:   endTime,
		TenantId:  tid,
	}

	return esjson.LoadQuery(esjson.EVENTS_AGGREGATION, &params)
}

var ignoreEvent = map[string]struct{}{
	"CreateLogStream": {},
	"PutEvaluations":  {},
}

func isIgnoredEvent(eventName string) bool {
	if _, ok := ignoreEvent[eventName]; ok {
		return true
	}
	return false
}
