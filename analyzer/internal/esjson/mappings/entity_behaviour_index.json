{"mappings": {"properties": {"id": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "accountIds": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "tenantId": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "entityType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "activityOverTime": {"type": "nested", "properties": {"date": {"type": "date", "format": "date_time"}, "count": {"type": "integer"}}}, "entityName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "resourceTypes": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "eventSources": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "sourceIps": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "sourceApps": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "createdAt": {"type": "date", "format": "date_time"}, "updatedAt": {"type": "date", "format": "date_time"}, "eventNames": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}}