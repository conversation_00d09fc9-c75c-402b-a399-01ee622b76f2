package services_test

import (
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/precize/analyzer/config"
	"github.com/precize/analyzer/internal/elasticsearch"
	"github.com/precize/analyzer/internal/services"
	"github.com/precize/analyzer/internal/utils"
	"github.com/precize/analyzer/pkg/kv"
	"github.com/precize/logger"
)

func TestAnomalyDetection(t *testing.T) {
	logger.InitializeLogs("analyzer", false)
	config.LoadConfig("")
	kv, err := kv.NewBadgerDB()
	if err != nil {
		t.Fatal(err)
	}
	defer kv.Close()

	es := elasticsearch.NewClient()
	if _, err := es.Ping(); err != nil {
		t.Fatal(err)
	}
	tenantID := "0R8Da4gBoELr5xpoQ6Y3"
	username := "<EMAIL>"
	startTime, err := time.Parse(utils.ESTimeLayout, "2025-03-31T15:12:36.513Z")
	if err != nil {
		t.Fatal(err)
	}
	endTime := time.Now()
	lastCollectedAtKey := fmt.Sprintf("last_collected_at_%s", tenantID)
	err = kv.Set([]byte(lastCollectedAtKey), "2025-04-01T15:12:36.513Z")
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error setting last_collected_at for tenant %s: %s", tenantID, err))
	}
	entityBehaviours, err := elasticsearch.FetchEntityBehaviour(es, tenantID, username, startTime, endTime)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println("Entity Behaviours:", entityBehaviours)

	var entityBehavioursCopy []*elasticsearch.EntityBehaviour
	for _, behaviour := range entityBehaviours {
		entityBehavioursCopy = append(entityBehavioursCopy, behaviour)
	}

	elasticsearch.SetEntityBehaviour(tenantID, entityBehavioursCopy)

	events, err := es.GetEvents(tenantID, startTime, endTime)
	if err != nil {
		t.Fatal(err)
	}

	if len(events) == 0 {
		t.Fatal("No events found")
	}

	fmt.Println("Number of events:", len(events))

	anomalyChan := make(chan elasticsearch.Anomaly, 1000)

	var wg sync.WaitGroup

	wg.Add(1)
	go func() {
		defer wg.Done()
		fmt.Println("Processing anomalies")
		services.ProcessAnomalies(es, anomalyChan)
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		defer close(anomalyChan)

		fmt.Println("Processing events")
		for _, event := range events {
			for _, entityBehaviour := range entityBehaviours {
				if entityBehaviour.TenantID == event.TenantID && entityBehaviour.EntityName == event.Username {
					eventAnomalies := services.DetectAnomalies(*entityBehaviour, event)
					for anomaly := range eventAnomalies {
						anomalyChan <- anomaly
					}
				}
			}
		}
	}()

	wg.Wait()
}
