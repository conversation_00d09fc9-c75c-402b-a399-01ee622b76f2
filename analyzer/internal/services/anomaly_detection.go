package services

import (
	"errors"
	"fmt"
	"math"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/precize/analyzer/internal/elasticsearch"
	"github.com/precize/analyzer/internal/utils"
	"github.com/precize/analyzer/pkg/kv"
	"github.com/precize/logger"
)

func ProcessAnomalies(es *elasticsearch.Client, anomalyChan chan elasticsearch.Anomaly) {
	anomalies := make(map[string]elasticsearch.Anomaly)
	for anomaly := range anomalyChan {
		id := utils.GenerateCombinedHashID(
			anomaly.AccountID,
			anomaly.TenantID,
			anomaly.Username,
			string(anomaly.AnomalyType),
			anomaly.AnomalyEntity,
		)

		if _, ok := anomalies[id]; !ok {
			anomalies[id] = anomaly
		} else {
			anomalies[id] = mergeAnomaly(anomalies[id], anomaly)
		}
	}

	logger.Print(logger.INFO, fmt.Sprintf("Total anomalies processed: %d", len(anomalies)))

	var keys []string
	for key := range anomalies {
		keys = append(keys, key)
	}

	foundAnomalies, notFoundAnomalies, err := es.FetchExistingAnomalies(keys)
	if err != nil {
		logger.Print(logger.ERROR, err.Error())
	}

	var anomalies_slice []elasticsearch.Anomaly

	for id, anomaly := range anomalies {
		if ok := slices.Contains(notFoundAnomalies, id); ok {
			anomalies_slice = append(anomalies_slice, anomaly)
		} else {
			for _, foundAnomaly := range foundAnomalies {
				if foundAnomaly.ID == id {
					mergedAnomaly := mergeAnomaly(foundAnomaly, anomaly)
					err := ValidateAnomaly(es, &mergedAnomaly)
					if err != nil {
						logger.Print(logger.ERROR, err.Error())
						logger.Print(logger.ERROR, fmt.Sprintf("Validation failed for anomaly id: %s", id))
						continue
					}
					anomalies_slice = append(anomalies_slice, mergedAnomaly)
				}
			}
		}
	}

	logger.Print(logger.INFO, fmt.Sprintf("Total new anomalies: %d", len(notFoundAnomalies)))
	logger.Print(logger.INFO, fmt.Sprintf("Total old anomalies: %d", len(foundAnomalies)))

	if err := es.StoreAnomalies(anomalies_slice); err != nil {
		logger.Print(logger.ERROR, err.Error())
	}
	logger.Print(logger.INFO, "Anomalies stored successfully")

	if err := es.StoreAnomaliesIntoIncidents(anomalies_slice); err != nil {
		logger.Print(logger.ERROR, err.Error())
	}
	logger.Print(logger.INFO, "Anomalies stored into incidents successfully")
}

func ValidateAnomaly(es *elasticsearch.Client, anomaly *elasticsearch.Anomaly) error {
	logger.Print(logger.INFO, fmt.Sprintf("Validation was triggered for anomaly id: %s\n", anomaly.ID))
	if anomaly.Count < 2 {
		return nil
	}

	currentEntityBehaviour := elasticsearch.GetEntityBehaviourByUsername(anomaly.TenantID, anomaly.Username)
	if currentEntityBehaviour == nil {
		logger.Print(logger.ERROR, "Entity behaviour not found")
		return errors.New("entity behaviour not found")
	}
	logger.Print(logger.INFO, fmt.Sprintf("Current entity behaviour: %v\n", currentEntityBehaviour))

	if currentEntityBehaviour.EntityType == "access_key" {
		return nil
	}

	kv := kv.GetBadgerDB()
	lastCollectedAtKey := fmt.Sprintf("last_collected_at_%s", anomaly.TenantID)
	lastCollectedAt, err := kv.Get([]byte(lastCollectedAtKey))

	var lastCollectedAtTime time.Time
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error getting last_collected_at for tenant %s: %s", anomaly.TenantID, err))
		return err
	}
	if lastCollectedAt == "" {
		lastCollectedAtTime = time.Now().Add(-7 * 24 * time.Hour).UTC()
	} else {
		lastCollectedAtTime, err = time.Parse(utils.ESTimeLayout, lastCollectedAt)
		if err != nil {
			logger.Print(logger.ERROR, fmt.Sprintf("Invalid last_collected_at format for tenant %s, resetting to default", anomaly.TenantID))
			lastCollectedAtTime = time.Now().Add(-7 * 24 * time.Hour).UTC()
		}
	}

	foundMatch := false

	// Check past behaviours by going back 2, 4, then 6 months
	for months := 2; months <= 6; months += 2 {
		startTime := time.Now().AddDate(0, -months, 0)

		behaviours, err := elasticsearch.FetchEntityBehaviour(es, anomaly.TenantID, anomaly.Username, startTime, lastCollectedAtTime)
		if err != nil {
			logger.Print(logger.ERROR, "Error fetching entity behaviour: ", err)
			continue
		}

		if behaviours == nil {
			logger.Print(logger.INFO, fmt.Sprintf("No behaviours found in past %d months", months))
			continue
		}

		if checkAndUpdateAnomaly(anomaly, behaviours, currentEntityBehaviour) {
			foundMatch = true
			break
		}
	}

	if !foundMatch {
		logger.Print(logger.INFO, "No matching historical behaviour found — anomaly is considered valid")
	}

	// Update and store
	if err := elasticsearch.UpdateEntityBehaviourByUsername(es, anomaly.TenantID, anomaly.Username, currentEntityBehaviour); err != nil {
		logger.Print(logger.ERROR, err.Error())
		return err
	}

	if err := es.StoreEntityBehaviour([]*elasticsearch.EntityBehaviour{currentEntityBehaviour}); err != nil {
		logger.Print(logger.ERROR, err.Error())
		return err
	}

	return nil
}

func checkAndUpdateAnomaly(anomaly *elasticsearch.Anomaly, behaviours []*elasticsearch.EntityBehaviour, current *elasticsearch.EntityBehaviour) bool {
	switch anomaly.AnomalyType {
	case "UnusualEventSource":
		for _, b := range behaviours {
			if slices.Contains(b.EventSources, anomaly.AnomalyEntity) {
				anomaly.Severity = elasticsearch.ANOMALY_SERVERITY_MEDIUM
				current.EventSources = append(current.EventSources, anomaly.AnomalyEntity)
				return true
			}
		}
	case "UnusualRegion":
		for _, b := range behaviours {
			if slices.Contains(b.Regions, anomaly.AnomalyEntity) {
				anomaly.Severity = elasticsearch.ANOMALY_SERVERITY_MEDIUM
				current.Regions = append(current.Regions, anomaly.AnomalyEntity)
				return true
			}
		}
	case "UnusualSourceApp":
		for _, b := range behaviours {
			if slices.Contains(b.SourceApps, anomaly.AnomalyEntity) {
				anomaly.Severity = elasticsearch.ANOMALY_SERVERITY_MEDIUM
				current.SourceApps = append(current.SourceApps, anomaly.AnomalyEntity)
				return true
			}
		}
	case "HighFrequencyOfEvents":
		for _, b := range behaviours {
			if len(b.ActivityOverTime) > 0 {
				recentDayEvents, err := getMostRecentDayEvents(b.ActivityOverTime)
				if err != nil {
					continue
				}
				activityOverTime := b.ActivityOverTime[:len(b.ActivityOverTime)-1]
				mean, stdDev := calculateStats(activityOverTime)
				if float64(recentDayEvents) > mean+2*stdDev {
					anomaly.Severity = elasticsearch.ANOMALY_SERVERITY_MEDIUM
					current.ActivityOverTime = append(current.ActivityOverTime, activityOverTime...)
					return true
				}
			}
		}
	}
	return false
}

func DetectAnomalies(entityBehaviour elasticsearch.EntityBehaviour, event elasticsearch.Event) chan elasticsearch.Anomaly {
	anomalyChan := make(chan elasticsearch.Anomaly)
	currentTimestamp := time.Now().Format(utils.ESTimeLayout)

	go func() {
		defer close(anomalyChan)

		// Strategy 0: High Error Count
		if event.ErrorCount > 0 && event.ErrorCount%10 == 0 {
			desc, ok := elasticsearch.GetAnomalyDescription(elasticsearch.HIGH_ERROR_COUNT)
			if !ok {
				desc = "High error count"
			}
			anomalyChan <- elasticsearch.Anomaly{
				ID:                 utils.GenerateCombinedHashID(event.AccountID, event.TenantID, event.Username, "HighErrorCount", strconv.Itoa(event.ErrorCount)),
				AccountID:          event.AccountID,
				TenantID:           event.TenantID,
				Username:           event.Username,
				AnomalyType:        elasticsearch.HIGH_ERROR_COUNT,
				AnomalyDescription: desc,
				RefDoc:             event.DocIDs,
				LastDetectedAt:     currentTimestamp,
				FirstDetectedAt:    event.CreatedAt,
				AnomalyEntity:      strconv.Itoa(event.ErrorCount),
				AdditionalDetails: utils.ToJSON(map[string]int{
					"ErrorCount": event.ErrorCount,
				}),
				Count:       1,
				ServiceCode: event.ServiceCode,
				Severity:    elasticsearch.ANOMALY_SERVERITY_HIGH,
			}
		}

		// Strategy 1: Unusual Event Source
		for _, eventSource := range event.EventSource {
			if entityBehaviour.EventSources == nil || len(entityBehaviour.EventSources) == 0 {
				break
			}
			if !slices.Contains(entityBehaviour.EventSources, eventSource) {
				additionalDetails := utils.ToJSON(map[string]any{
					"UsualEventSources":  entityBehaviour.EventSources,
					"UnusualEventSource": eventSource,
				})
				desc, ok := elasticsearch.GetAnomalyDescription(elasticsearch.UNUSUAL_EVENT_SOURCE)
				if !ok {
					desc = "Unusual Event Source"
				}
				anomalyChan <- elasticsearch.Anomaly{
					ID:                 utils.GenerateCombinedHashID(event.AccountID, event.TenantID, event.Username, "UnusualEventSource", eventSource),
					AccountID:          event.AccountID,
					TenantID:           event.TenantID,
					Username:           event.Username,
					AnomalyType:        elasticsearch.UNUSUAL_EVENT_SOURCE,
					AnomalyDescription: desc,
					RefDoc:             event.DocIDs,
					LastDetectedAt:     currentTimestamp,
					FirstDetectedAt:    event.CreatedAt,
					AnomalyEntity:      eventSource,
					AdditionalDetails:  additionalDetails,
					Count:              1,
					ServiceCode:        event.ServiceCode,
					Severity:           elasticsearch.ANOMALY_SERVERITY_LOW,
				}
			}
		}

		// Strategy 2: Unusual Region
		for _, region := range event.Region {
			if entityBehaviour.Regions == nil || len(entityBehaviour.Regions) == 0 {
				break
			}
			if region == "" {
				continue
			}
			if !slices.Contains(entityBehaviour.Regions, region) {
				additionalDetails := utils.ToJSON(map[string]any{
					"UsualRegions":  entityBehaviour.Regions,
					"UnusualRegion": region,
				})
				desc, ok := elasticsearch.GetAnomalyDescription(elasticsearch.UNUSUAL_REGION)
				if !ok {
					desc = "Unusual Region"
				}
				anomalyChan <- elasticsearch.Anomaly{
					ID:                 utils.GenerateCombinedHashID(event.AccountID, event.TenantID, event.Username, "UnusualRegion", region),
					AccountID:          event.AccountID,
					TenantID:           event.TenantID,
					Username:           event.Username,
					AnomalyType:        elasticsearch.UNUSUAL_REGION,
					AnomalyDescription: desc,
					RefDoc:             event.DocIDs,
					LastDetectedAt:     currentTimestamp,
					FirstDetectedAt:    event.CreatedAt,
					AnomalyEntity:      region,
					AdditionalDetails:  additionalDetails,
					Count:              1,
					ServiceCode:        event.ServiceCode,
					Severity:           elasticsearch.ANOMALY_SERVERITY_LOW,
				}
			}
		}

		// Strategy 3: Unusual Source Application
		for _, sourceApp := range event.SourceApp {
			if entityBehaviour.SourceApps == nil || len(entityBehaviour.SourceApps) == 0 {
				break
			}
			if !slices.Contains(entityBehaviour.SourceApps, sourceApp) {
				additionalDetails := utils.ToJSON(map[string]any{
					"UsualSourceApps":  entityBehaviour.SourceApps,
					"UnusualSourceApp": sourceApp,
				})
				desc, ok := elasticsearch.GetAnomalyDescription(elasticsearch.UNUSUAL_SOURCE_APP)
				if !ok {
					desc = "Unusual Source App"
				}
				anomalyChan <- elasticsearch.Anomaly{
					ID:                 utils.GenerateCombinedHashID(event.AccountID, event.TenantID, event.Username, "UnusualSourceApp", sourceApp),
					AccountID:          event.AccountID,
					TenantID:           event.TenantID,
					Username:           event.Username,
					AnomalyType:        elasticsearch.UNUSUAL_SOURCE_APP,
					AnomalyDescription: desc,
					RefDoc:             event.DocIDs,
					LastDetectedAt:     currentTimestamp,
					FirstDetectedAt:    event.CreatedAt,
					AnomalyEntity:      sourceApp,
					AdditionalDetails:  additionalDetails,
					Count:              1,
					ServiceCode:        event.ServiceCode,
					Severity:           elasticsearch.ANOMALY_SERVERITY_LOW,
				}
			}
		}

		// Strategy 4: Shared Access Key
		// if entityBehaviour.EntityType == "access_key" {
		// 	key, ok := event.Additional["accessKeyId"].(string)
		// 	for _, sourceIP := range event.SourceIp {
		// 		if ok && !slices.Contains(entityBehaviour.SourceIPs, sourceIP) {
		// 			anomalyChan <- elasticsearch.Anomaly{
		// 				ID:                 utils.GenerateCombinedHashID(event.AccountID, event.TenantID, event.Username, "SharedAccessKey", key),
		// 				AccountID:          event.AccountID,
		// 				TenantID:           event.TenantID,
		// 				Username:           event.Username,
		// 				AnomalyType:        "SharedAccessKey",
		// 				AnomalyDescription: "AWS AcessKey is being share: " + key,
		// 				RefDoc:             event.DocIDs,
		// 				LastDetectedAt:     currentTimestamp,
		// 				FirstDetectedAt:    event.CreatedAt,
		// 				AnomalyEntity: 		key,
		// 				Count:              1,
		// 				ServiceID:          event.ServiceCode,
		// 				Severity:           elasticsearch.ANOMALY_SERVERITY_HIGH,
		// 			}
		// 		}
		// 	}
		// }

		// Strategy 5: High Frequency of Events
		if len(entityBehaviour.ActivityOverTime) > 0 && entityBehaviour.ActivityOverTime != nil {
			additionalDetails := utils.ToJSON(map[string]any{"ActivityOverTime": entityBehaviour.ActivityOverTime})
			recentDayEvents, err := getMostRecentDayEvents(entityBehaviour.ActivityOverTime)
			activityOverTime := entityBehaviour.ActivityOverTime[:len(entityBehaviour.ActivityOverTime)-1]
			desc, ok := elasticsearch.GetAnomalyDescription(elasticsearch.HIGH_FREQUENCY_OF_EVENTS)
			if !ok {
				desc = "High Frequency of Events"
			}
			if err == nil {
				mean, stdDev := calculateStats(activityOverTime)
				if float64(recentDayEvents) > mean+2*stdDev {
					anomalyChan <- elasticsearch.Anomaly{
						ID:                 utils.GenerateCombinedHashID(event.AccountID, event.TenantID, event.Username, "HighFrequencyOfEvents", event.Username),
						AccountID:          event.AccountID,
						TenantID:           event.TenantID,
						Username:           event.Username,
						AnomalyType:        elasticsearch.HIGH_FREQUENCY_OF_EVENTS,
						AnomalyDescription: desc,
						RefDoc:             event.DocIDs,
						LastDetectedAt:     currentTimestamp,
						FirstDetectedAt:    event.CreatedAt,
						AdditionalDetails:  additionalDetails,
						AnomalyEntity:      event.Username,
						Count:              1,
						ServiceCode:        event.ServiceCode,
						Severity:           elasticsearch.ANOMALY_SERVERITY_LOW,
					}
				}
			}
		}

		// Strategy 6: Shared Service Account via IP for GCP
		if strings.HasSuffix(event.Username, ".gserviceaccount.com") {
			for _, sourceIP := range event.SourceIp {
				if !slices.Contains(entityBehaviour.SourceIPs, sourceIP) {
					if ok := isWithinCIDRRange(entityBehaviour.SourceIPs, sourceIP); ok {
						continue
					}
					additionalDetails := utils.ToJSON(map[string]any{
						"UsualSourceIPs":  entityBehaviour.SourceIPs,
						"UnusualSourceIP": sourceIP,
					})
					desc, ok := elasticsearch.GetAnomalyDescription(elasticsearch.SHARED_SERVICE_ACCOUNT_VIA_IP)
					if !ok {
						desc = "Shared Service Account via IP for GCP"
					}
					anomalyChan <- elasticsearch.Anomaly{
						ID:                 utils.GenerateCombinedHashID(event.AccountID, event.TenantID, event.Username, "SharedServiceAccountViaIP", "Shared service account via IP: "+sourceIP),
						AccountID:          event.AccountID,
						TenantID:           event.TenantID,
						Username:           event.Username,
						AnomalyType:        elasticsearch.SHARED_SERVICE_ACCOUNT_VIA_IP,
						AnomalyDescription: desc,
						RefDoc:             event.DocIDs,
						LastDetectedAt:     currentTimestamp,
						FirstDetectedAt:    event.CreatedAt,
						AdditionalDetails:  additionalDetails,
						AnomalyEntity:      event.Username,
						Count:              1,
						ServiceCode:        event.ServiceCode,
						Severity:           elasticsearch.ANOMALY_SERVERITY_MEDIUM,
					}
				}
			}
		}

		// Strategy 7: Use of personal email addresses (e.g., Gmail, Yahoo, Outlook, etc.)
		personalEmailDomains := []string{
			"@gmail.com",
			"@yahoo.com",
			"@outlook.com",
			"@hotmail.com",
			"@aol.com",
			"@icloud.com",
			"@protonmail.com",
			"@zoho.com",
			"@gmx.com",
		}

		if event.Username != "" {
			lowerUsername := strings.ToLower(event.Username)
			for _, domain := range personalEmailDomains {
				if strings.HasSuffix(lowerUsername, domain) {
					desc, ok := elasticsearch.GetAnomalyDescription(elasticsearch.PERSONAL_EMAIL_USAGE)
					if !ok {
						desc = "Personal Email Usuage"
					}
					anomalyChan <- elasticsearch.Anomaly{
						ID:                 utils.GenerateCombinedHashID(event.AccountID, event.TenantID, event.Username, "PersonalEmailUsage", "Personal email usage"),
						AccountID:          event.AccountID,
						TenantID:           event.TenantID,
						Username:           event.Username,
						AnomalyType:        elasticsearch.PERSONAL_EMAIL_USAGE,
						AnomalyDescription: desc,
						RefDoc:             event.DocIDs,
						LastDetectedAt:     currentTimestamp,
						FirstDetectedAt:    event.CreatedAt,
						AdditionalDetails: utils.ToJSON(map[string]any{
							"PersonalEmail": event.Username,
						}),
						AnomalyEntity: event.Username,
						Count:         1,
						ServiceCode:   event.ServiceCode,
						Severity:      elasticsearch.ANOMALY_SERVERITY_MEDIUM,
					}
					break
				}
			}
		}
	}()

	return anomalyChan
}

func calculateStats(activityOverTime []elasticsearch.ActivityOverTime) (mean, stdDev float64) {
	sum := 0
	count := 0
	var values []float64

	for _, entry := range activityOverTime {
		sum += entry.Count
		count++
		values = append(values, float64(entry.Count))
	}

	if count == 0 {
		return 0, 0
	}

	mean = float64(sum) / float64(count)
	varianceSum := 0.0
	for _, v := range values {
		varianceSum += math.Pow(v-mean, 2)
	}
	stdDev = math.Sqrt(varianceSum / float64(count))

	return mean, stdDev
}

func getMostRecentDayEvents(activityOverTime []elasticsearch.ActivityOverTime) (int, error) {
	var mostRecentDate time.Time
	var recentDayEvents int

	for _, entry := range activityOverTime {
		parsedDate, err := time.Parse("2006-01-02", entry.Date)
		if err != nil {
			continue
		}
		if parsedDate.After(mostRecentDate) {
			mostRecentDate = parsedDate
			recentDayEvents = entry.Count
		}
	}

	if mostRecentDate.IsZero() {
		return 0, fmt.Errorf("no valid dates found")
	}

	return recentDayEvents, nil
}

func mergeAnomaly(a elasticsearch.Anomaly, b elasticsearch.Anomaly) elasticsearch.Anomaly {
	mergedRefDocs := utils.MergeStringSlice(a.RefDoc, b.RefDoc)

	mergedCount := a.Count + b.Count

	mergedLastDetectedAt := b.LastDetectedAt
	lastDetectedAtA, err1 := time.Parse(utils.ESTimeLayout, a.LastDetectedAt)
	lastDetectedAtB, err2 := time.Parse(utils.ESTimeLayout, b.LastDetectedAt)
	if err1 == nil && err2 == nil {
		if lastDetectedAtA.After(lastDetectedAtB) {
			mergedLastDetectedAt = a.LastDetectedAt
		} else {
			mergedLastDetectedAt = b.LastDetectedAt
		}
	} else {
		logger.Print(logger.ERROR, err1.Error(), err2.Error())
	}

	mergedFirstDetectedAt := a.FirstDetectedAt
	firstDetectedAtA, err1 := time.Parse(utils.ESTimeLayout, a.FirstDetectedAt)
	firstDetectedAtB, err2 := time.Parse(utils.ESTimeLayout, b.FirstDetectedAt)
	if err1 == nil && err2 == nil {
		if firstDetectedAtA.Before(firstDetectedAtB) {
			mergedFirstDetectedAt = a.FirstDetectedAt
		} else {
			mergedFirstDetectedAt = b.FirstDetectedAt
		}
	} else {
		logger.Print(logger.ERROR, err1.Error(), err2.Error())
	}

	mergedAdditionalDetails := utils.MergeJSONStrings(a.AdditionalDetails, b.AdditionalDetails)

	var severity string
	if a.Severity == elasticsearch.ANOMALY_SERVERITY_HIGH || b.Severity == elasticsearch.ANOMALY_SERVERITY_HIGH {
		severity = elasticsearch.ANOMALY_SERVERITY_HIGH
	} else if a.Severity == elasticsearch.ANOMALY_SERVERITY_MEDIUM || b.Severity == elasticsearch.ANOMALY_SERVERITY_MEDIUM {
		severity = elasticsearch.ANOMALY_SERVERITY_MEDIUM
	} else {
		severity = elasticsearch.ANOMALY_SERVERITY_LOW
	}

	return elasticsearch.Anomaly{
		ID:                 a.ID,
		AccountID:          a.AccountID,
		TenantID:           a.TenantID,
		Username:           a.Username,
		AnomalyEntity:      a.AnomalyEntity,
		AnomalyType:        a.AnomalyType,
		AnomalyDescription: a.AnomalyDescription,
		RefDoc:             mergedRefDocs,
		Count:              mergedCount,
		LastDetectedAt:     mergedLastDetectedAt,
		FirstDetectedAt:    mergedFirstDetectedAt,
		AdditionalDetails:  mergedAdditionalDetails,
		ServiceCode:        a.ServiceCode,
		Severity:           severity,
	}
}

func isWithinCIDRRange(cidrRanges []string, ip string) bool {
	for _, cidrRange := range cidrRanges {
		ipOctets := strings.Split(ip, ".")
		cidrOctets := strings.Split(cidrRange, ".")

		if len(ipOctets) != 4 || len(cidrOctets) != 4 {
			continue
		}

		ipOctets[3] = "0"
		cidrOctets[3] = "0"

		if ipOctets[0] == cidrOctets[0] && ipOctets[1] == cidrOctets[1] {
			return true
		}
	}
	return false
}
