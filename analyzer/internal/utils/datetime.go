package utils

import (
	"fmt"
	"time"
)

const (
	ESTimeLayout = "2006-01-02T15:04:05.000Z"
)

func GetTimeRange(startTime string, d time.Duration) (string, string, error) {
	var start time.Time
	var err error

	start, err = time.Parse(ESTimeLayout, startTime)

	if err != nil {
		return "", "", fmt.Errorf("invalid time format: %w", err)
	}

	end := start.Add(d)

	return start.UTC().Format(ESTimeLayout), end.UTC().Format(ESTimeLayout), nil
}
