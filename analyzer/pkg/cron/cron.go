package cron

import (
	"fmt"
	"time"

	"github.com/precize/logger"
	cron "github.com/robfig/cron/v3"
)

var cronJobs []*CronJob

func New() *cron.Cron {
	return cron.New()
}

func RegisterCronJob(cj *<PERSON>ronJob) {
	cronJobs = append(cronJobs, cj)
	cj.Update()
}

func NotifyCronJobs() {
	for _, cronJob := range cronJobs {
		cronJob.Update()
	}
}

type CronJob struct {
	CronScheduler *cron.Cron
	JobID         cron.EntryID
	Interval      func() time.Duration
	ExecuteFunc   func()
}

func NewCronJob(scheduler *cron.Cron, intervalFunc func() time.Duration, executeFunc func()) *CronJob {
	return &CronJob{
		CronScheduler: scheduler,
		Interval:      intervalFunc,
		ExecuteFunc:   executeFunc,
	}
}

func (cj *<PERSON>ronJob) Update() {
	if cj.JobID != 0 {
		cj.CronScheduler.Remove(cj.JobID)
	}

	logger.Print(logger.INFO, fmt.Sprintf("Updating cron job with interval: %s", cj.Interval()))
	cj.JobID, _ = cj.CronScheduler.AddFunc(fmt.Sprintf("@every %s", cj.Interval()), cj.ExecuteFunc)
}
