package context

import (
	"regexp"
	"strings"

	"github.com/precize/common"
)

func GetAppNameFromValue(str string, opts ...ContextCriteriaOptions) (app string) {

	var (
		matched    string
		matchedLen int
		criteria   ContextCriteria
	)

	for _, opt := range opts {
		opt(&criteria)
	}

	str = strings.ToLower(str)

	for appName, values := range appValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex := regexp.MustCompile(val)
			if regex.MatchString(str) && len(appName) > matchedLen {
				if !unsupportedApp(appName, criteria) {
					matchedLen = len(appName)
					matched = appName
				}
			}
		}
	}

	if len(matched) > 0 {
		app = matched
	}

	return
}

func GetAppNameListFromValue(str string, opts ...ContextCriteriaOptions) []string {

	var (
		appNames = make([]string, 0)
		criteria ContextCriteria
	)

	str = strings.ToLower(str)

	for _, opt := range opts {
		opt(&criteria)
	}

	for app, values := range appValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex := regexp.MustCompile(val)
			if regex.MatchString(str) {
				if !unsupportedApp(app, criteria) {
					appNames = append(appNames, app)
				}
			}
		}
	}

	return appNames
}

func GetUniqueAppContext(resourceContextDoc *common.ResourceContextInsertDoc) (app []string) {

	uniqueApp := make(map[string]struct{})

	for _, v := range resourceContextDoc.ResourceAppTypes.DefinedApp {
		uniqueApp[v.Name] = struct{}{}
	}
	for _, v := range resourceContextDoc.ResourceAppTypes.DerivedApp {
		uniqueApp[v.Name] = struct{}{}
	}

	for appName := range uniqueApp {
		app = append(app, appName)
	}

	return
}

func AddApplicationToGlobalApps(application string) {

	globalValuesMutex.Lock()

	var globallyExists bool

	for _, defaultAppValue := range defaultAppValues {
		subApps := strings.Split(defaultAppValue, ",")
		for _, subApp := range subApps {
			if strings.ToLower(subApp) == strings.ToLower(application) {
				globallyExists = true
				break
			}
		}

		if globallyExists {
			break
		}
	}

	if !globallyExists {
		defaultAppValues = append(defaultAppValues, application)
	}

	globalValuesMutex.Unlock()
}

func unsupportedApp(appName string, criteria ContextCriteria) bool {

	switch appName {
	case JENKINS_APP, ARGO_APP, CICD_APP:
		switch criteria.ResourceType {
		case common.AWS_EC2_RESOURCE_TYPE, common.GCP_INSTANCE_RESOURCE_TYPE, common.AZURE_VM_RESOURCE_TYPE:
		default:
			return true
		}
	case TERRAFORM_APP:
		switch criteria.ResourceType {
		case common.AZURE_GRAPHAPP_RESOURCE_TYPE, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, common.GCP_SAPOLICYBINDING_RESOURCE_TYPE, common.AWS_IAM_ROLE_RESOURCE_TYPE, common.AWS_IAM_USER_RESOURCE_TYPE:
		default:
			return true
		}
	}

	return false
}
