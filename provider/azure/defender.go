package azure

import (
	"bytes"
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

const (
	bufferTime = 24 * time.Hour
)

func ProcessDefenderRecommendations(tenantID, envID string, defenderRecStartTime, defenderRecEndTime, tenantStartTime time.Time) {

	var (
		firstEverIteration      bool
		recommendationSkipToken string
	)

	if defenderRecEndTime.Sub(defenderRecStartTime) < bufferTime {
		return
	}

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.DEFENDER_REC, tenantStartTime)

	if (defenderRecEndTime.Sub(defenderRecStartTime)) >= defenderRecEndTime.Sub(defaultTime) {
		defenderRecStartTime = defaultTime
		firstEverIteration = true
	}

	logger.Print(logger.INFO, "Fetching Defender Recommendations from "+common.DateTime(defenderRecStartTime)+" to "+common.DateTime(defenderRecEndTime), []string{tenantID})

	for {

		azureResourceGraphUrl := `/precize/private/azure/getResourcesByFilter/` + envID
		defenderRecommendationsQuery := `securityresources | where type == 'microsoft.security/assessments' | project id, name, type, tenantId, kind, location, resourceGroup, subscriptionId, managedBy, sku, plan, properties, tags, identity, zones, extendedLocation, statusChangeDate = properties.status.statusChangeDate, status=properties.status.code, sourceRisk=properties.risk.level | where sourceRisk != "Information" and sourceRisk != "Low" | where statusChangeDate >= todatetime('` + elastic.DateTime(defenderRecStartTime) + `') and statusChangeDate <= todatetime('` + elastic.DateTime(defenderRecEndTime) + `')`

		if firstEverIteration {
			defenderRecommendationsQuery += ` | where status == 'Unhealthy'` // Healthy resources (or Completed) are those for which a particular incident is not applicable
		}

		resourceGraphRequest := ResourceGraphRequest{
			Query: defenderRecommendationsQuery,
		}

		if len(recommendationSkipToken) > 0 {
			resourceGraphRequest.Options.SkipToken = recommendationSkipToken
		}

		var (
			recommendationBuf      bytes.Buffer
			defRecommendationsResp common.DefenderRecommendationResp
		)

		err := json.NewEncoder(&recommendationBuf).Encode(resourceGraphRequest)
		if err != nil {
			logger.Print(logger.ERROR, "Got error encoding request body", []string{tenantID}, err)
			break
		}

		resp, err := transport.SendRequestToServer("POST", azureResourceGraphUrl, nil, &recommendationBuf)
		if err != nil {
			break
		}

		if err = json.Unmarshal(resp, &defRecommendationsResp); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
			break
		}

		if err := insertDefenderRecommendations(defRecommendationsResp.Data, tenantID, defenderRecEndTime, envID); err != nil {
			break
		}

		if len(defRecommendationsResp.SkipToken) > 0 {
			recommendationSkipToken = defRecommendationsResp.SkipToken
		} else {
			break
		}
	}

	logger.Print(logger.INFO, "Fetching Defender Security Alerts from "+common.DateTime(defenderRecStartTime)+" to "+common.DateTime(defenderRecEndTime), []string{tenantID})

	var alertSubscriptionSkipToken string

	for {

		azureResourceGraphUrl := `/precize/private/azure/getResourcesByFilter/` + envID
		defenderAlertsQuery := `securityresources | where type =~ 'microsoft.security/locations/alerts' | where properties.Severity!='Low' and properties.Severity!='Informational' | where properties.TimeGeneratedUtc >= todatetime('` + elastic.DateTime(defenderRecStartTime) + `') and properties.TimeGeneratedUtc <= todatetime('` + elastic.DateTime(defenderRecEndTime) + `') | summarize by subscriptionId | project subscriptionId`

		resourceGraphRequest := ResourceGraphRequest{
			Query: defenderAlertsQuery,
		}

		if len(alertSubscriptionSkipToken) > 0 {
			resourceGraphRequest.Options.SkipToken = alertSubscriptionSkipToken
		}

		var (
			alertSubscriptionBuf bytes.Buffer
			defAlertsSubResp     common.DefenderAlertsSubscriptionsResp
		)

		err := json.NewEncoder(&alertSubscriptionBuf).Encode(resourceGraphRequest)
		if err != nil {
			logger.Print(logger.ERROR, "Got error encoding request body", []string{tenantID}, err)
			break
		}

		resp, err := transport.SendRequestToServer("POST", azureResourceGraphUrl, nil, &alertSubscriptionBuf)
		if err != nil {
			break
		}

		if err = json.Unmarshal(resp, &defAlertsSubResp); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
			break
		}

		securityAlertsUrl := `/precize/private/azure/defenderRecommendations/securityAlerts/` + envID

		for _, subData := range defAlertsSubResp.Data {

			var alertSkipToken string

			for {

				securityAlertRequest := map[string]string{
					"subscriptionId": subData.SubscriptionID,
				}

				if len(alertSkipToken) > 0 {
					securityAlertRequest["skipToken"] = alertSkipToken
				}

				var (
					alertBuf      bytes.Buffer
					defAlertsResp common.DefenderAlertsResp
				)

				err := json.NewEncoder(&alertBuf).Encode(securityAlertRequest)
				if err != nil {
					logger.Print(logger.ERROR, "Got error encoding request body", []string{tenantID}, err)
					break
				}

				resp, err := transport.SendRequestToServer("POST", securityAlertsUrl, nil, &alertBuf)
				if err != nil {
					break
				}

				if err = json.Unmarshal(resp, &defAlertsResp); err != nil {
					logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
					break
				}

				if err := insertDefenderAlerts(defAlertsResp.Value, tenantID, subData.SubscriptionID, defenderRecStartTime, defenderRecEndTime, firstEverIteration); err != nil {
					break
				}

				if len(defAlertsResp.NextLink) > 0 {
					linkSplit := strings.Split(defAlertsResp.NextLink, "skiptoken=")
					if len(linkSplit) > 1 {
						alertSkipToken = linkSplit[len(linkSplit)-1]
					} else {
						break
					}
				} else {
					break
				}
			}
		}

		if len(defAlertsSubResp.SkipToken) > 0 {
			alertSubscriptionSkipToken = defAlertsSubResp.SkipToken
		} else {
			break
		}
	}

	firstEverIteration = false
	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.DEFENDER_REC, defenderRecEndTime)
}

func insertDefenderRecommendations(defenderRecommendations []common.DefenderRecommendations, tenantID string, insertTime time.Time, envID string) error {

	var (
		bulkInsertQuery string
		currentCount    int
		maxRecords      = 10000
	)

	for _, data := range defenderRecommendations {

		createdAt, err := time.Parse(time.RFC3339, data.Properties.Status.FirstEvaluationDate)
		if err != nil {
			logger.Print(logger.ERROR, "Got error parsing date", []string{tenantID}, err)
			continue
		}

		updatedAt, err := time.Parse(time.RFC3339, data.StatusChangeDate)
		if err != nil {
			logger.Print(logger.ERROR, "Got error parsing date", []string{tenantID}, err)
			continue
		}

		jsonBytes, err := json.Marshal(data)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
			continue
		}

		var (
			serviceID  int
			sourceRisk string
		)

		switch data.Properties.ResourceDetails.Source {
		case "Azure":
			serviceID = common.AZURE_SERVICE_ID_INT
		case "AzureDevOps":
			serviceID = common.AZUREDEVOPS_SERVICE_ID_INT
		case "AWS":
			serviceID = common.AWS_SERVICE_ID_INT
		case "GCP":
			serviceID = common.GCP_SERVICE_ID_INT

		default:
			continue
		}

		recommendationFindings := make([]common.RecommendationFindings, 0)

		if len(data.Properties.AdditionalData.SubAssessmentsLink) > 0 {

			//Fetching Recommendation Findings

			azureResourceGraphUrl := `/precize/private/azure/getResourcesByFilter/` + envID
			defenderFindingsQuery := `securityresources | where type == 'microsoft.security/assessments/subassessments'| where id startswith '` + data.ID + `' | where properties.status.code == 'Unhealthy'`

			resourceGraphRequest := ResourceGraphRequest{
				Query: defenderFindingsQuery,
			}

			var (
				buf             bytes.Buffer
				defFindingsResp common.DefenderRecommendationFindingsResp
			)

			err := json.NewEncoder(&buf).Encode(resourceGraphRequest)
			if err != nil {
				logger.Print(logger.ERROR, "Got error encoding request body", []string{tenantID}, err)
				continue
			}

			resp, err := transport.SendRequestToServer("POST", azureResourceGraphUrl, nil, &buf)
			if err != nil {
				continue
			}

			if err = json.Unmarshal(resp, &defFindingsResp); err != nil {
				logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
				continue
			}

			for _, findingsData := range defFindingsResp.Data {

				if len(findingsData.Properties.VulnID) != 0 {
					var recommendationFinding common.RecommendationFindings

					recommendationFinding.Description = findingsData.Properties.Description
					recommendationFinding.ID = findingsData.Properties.VulnID
					recommendationFinding.Impact = findingsData.Properties.Impact
					recommendationFinding.Score = findingsData.Properties.AdditionalData.Score
					recommendationFinding.Severity = findingsData.Properties.Status.Severity

					recommendationFindings = append(recommendationFindings, recommendationFinding)
				}
			}
		}

		if sourceRisk = data.Properties.Risk.Level; len(sourceRisk) <= 0 {
			sourceRisk = common.NOTEVALUATED_RISK
		}

		if _, ok := common.ValidRiskLevel[sourceRisk]; !ok {
			sourceRisk = common.NOTEVALUATED_RISK
		}

		additionalData := map[string]any{
			"riskFactors":            data.Properties.Risk.RiskFactors,
			"recommendationFindings": recommendationFindings,
		}

		additionalDataBytes, err := json.Marshal(additionalData)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
			continue
		}

		description := data.Properties.Metadata.Description
		description = common.RemoveHTMLTagsFromString(description)

		status := common.INCIDENT_STATUS_OPEN
		if data.Properties.Status.Code == "Healthy" {
			status = common.INCIDENT_STATUS_RESOLVED
		}

		incidentDocID := common.GenerateCombinedHashID(tenantID, data.ID)

		incident := common.Incident{
			ID:             incidentDocID,
			AlertID:        data.ID,
			Issue:          data.Properties.DisplayName,
			AccountID:      data.SubscriptionID,
			EntityID:       strings.ToLower(data.Properties.ResourceDetails.ResourceID),
			Source:         common.DEFENDER_SOURCE,
			IssueSeverity:  data.Properties.Metadata.Severity,
			SourceRisk:     sourceRisk,
			CreatedAt:      elastic.DateTime(createdAt),
			UpdatedAt:      elastic.DateTime(updatedAt),
			ServiceID:      serviceID,
			Category:       strings.Join(data.Properties.Metadata.Categories, ", "),
			Description:    description,
			Status:         status,
			Stage:          "dc",
			TenantID:       tenantID,
			SourceJson:     string(jsonBytes),
			InsertTime:     elastic.DateTime(insertTime),
			AdditionalData: string(additionalDataBytes),
			IsIncident:     false,
		}

		incident.EntityType = common.EntityTypeMapping[data.Properties.ResourceDetails.ResourceType]

		if len(incident.EntityType) > 0 {

			if incident.EntityType == common.AZURE_SUBSCRIPTION_RESOURCE_TYPE {
				// Minor exception handling
				incident.EntityID = strings.TrimPrefix(incident.EntityID, "/subscriptions/")
				incident.AccountID = data.TenantID // Azure tenantId
			}

			incident.CrsID = common.GenerateCombinedHashIDCaseSensitive(tenantID, strconv.Itoa(serviceID), incident.AccountID, incident.EntityID, incident.EntityType)

			crsDoc, err := elastic.GetDocument(elastic.CLOUD_RESOURCE_STORE_INDEX, incident.CrsID)
			if err != nil {
				continue
			}

			if len(crsDoc) > 0 {

				b, err := json.Marshal(crsDoc)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
					continue
				}

				var crs common.CloudResourceStoreDoc

				if err = json.Unmarshal(b, &crs); err != nil {
					logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
					continue
				}

				incident.Environment = crs.Environment
				incident.Owner = crs.Owner
				incident.ResourceName = crs.ResourceName
				incident.AccountName = crs.AccountName
				incident.RelatedResources = crs.RelatedResources
			}
		} else {
			incident.EntityType = data.Properties.ResourceDetails.ResourceType
		}

		incidentBytes, _ := json.Marshal(incident)
		bulkInsertQuery = bulkInsertQuery + `{"index": {"_id": "` + incidentDocID + `"}}` + "\n"
		bulkInsertQuery += string(incidentBytes) + "\n"

		currentCount++

		if currentCount > maxRecords {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
				return err
			}

			logger.Print(logger.INFO, "Cloud Incident bulk API Successful for Azure Defender Recommendations for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
			currentCount = 0
			bulkInsertQuery = ""
		}
	}

	if currentCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
			return err
		}

		logger.Print(logger.INFO, "Cloud Incident bulk API Successful for Azure Defender Recommendations for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
	}

	return nil
}

func insertDefenderAlerts(defenderAlerts []common.DefenderAlert, tenantID, subscriptionID string, startTime, endTime time.Time, firstEverIteration bool) error {

	var (
		bulkInsertQuery, sourceRisk string
		currentCount                int
		maxRecords                  = 10000
	)

	for _, value := range defenderAlerts {

		generatedTime, err := time.Parse(time.RFC3339, value.Properties.TimeGeneratedUTC)
		if err != nil {
			logger.Print(logger.ERROR, "Got error parsing date", []string{tenantID}, err)
			continue
		}

		if generatedTime.Before(startTime) || generatedTime.After(endTime) {
			continue
		}

		status := common.INCIDENT_STATUS_OPEN
		if value.Properties.Status == "Resolved" || value.Properties.Status == "Dismissed" {
			if firstEverIteration {
				continue
			}

			status = common.INCIDENT_STATUS_RESOLVED
		}

		jsonBytes, err := json.Marshal(value)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
			continue
		}

		if sourceRisk = value.Properties.Severity; len(sourceRisk) <= 0 {
			sourceRisk = common.NOTEVALUATED_RISK
		}

		if _, ok := common.ValidRiskLevel[sourceRisk]; !ok {
			sourceRisk = common.NOTEVALUATED_RISK
		}

		if sourceRisk == common.LOW_RISK || sourceRisk == common.INFORMATIONAL_RISK {
			// avoid collection low priority incidents
			continue
		}

		additionalData := map[string]any{
			"extendedProperties": value.Properties.ExtendedProperties,
		}

		additionalDataBytes, err := json.Marshal(additionalData)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
			continue
		}

		description := value.Properties.Description
		description = common.RemoveHTMLTagsFromString(description)

		var entityID, azureTenantID string
		for _, r := range value.Properties.ResourceIdentifiers {
			if r.Type == "AzureResource" {
				entityID = strings.ToLower(r.AzureResourceID)
				azureTenantID = strings.ToLower(r.AzureResourceTenantID)
				break
			}
		}

		if len(entityID) <= 0 || len(azureTenantID) <= 0 {
			continue
		}

		incidentDocID := common.GenerateCombinedHashID(tenantID, value.ID)

		incident := common.Incident{
			ID:             incidentDocID,
			AlertID:        value.ID,
			Issue:          value.Properties.AlertDisplayName,
			AccountID:      subscriptionID,
			EntityID:       entityID,
			Source:         common.DEFENDER_SOURCE,
			IssueSeverity:  value.Properties.Severity,
			SourceRisk:     sourceRisk,
			CreatedAt:      elastic.DateTime(generatedTime),
			UpdatedAt:      elastic.DateTime(generatedTime),
			ServiceID:      common.AZURE_SERVICE_ID_INT,
			Category:       value.Properties.Intent,
			Description:    description,
			Status:         status,
			Stage:          "dc",
			TenantID:       tenantID,
			SourceJson:     string(jsonBytes),
			InsertTime:     elastic.DateTime(endTime),
			AdditionalData: string(additionalDataBytes),
			IsIncident:     true,
		}

		if resourceType, ok := value.Properties.ExtendedProperties["resourceType"].(string); ok && len(resourceType) > 0 {
			if resourceType == common.AZURE_SUBSCRIPTION_RESOURCE_TYPE {
				// Minor exception handling
				incident.EntityID = strings.TrimPrefix(incident.EntityID, "/subscriptions/")
				incident.AccountID = azureTenantID
			}
		}

		crsDoc, err := common.GetCRSDocFromResourceID(incident.EntityID, incident.AccountID, tenantID)
		if err != nil {
			continue
		}

		if len(crsDoc.ID) > 0 {
			incident.CrsID = crsDoc.ID
			incident.EntityType = crsDoc.EntityType
			incident.Environment = crsDoc.Environment
			incident.Owner = crsDoc.Owner
			incident.ResourceName = crsDoc.ResourceName
			incident.AccountName = crsDoc.AccountName
			incident.RelatedResources = crsDoc.RelatedResources
		}

		incidentBytes, _ := json.Marshal(incident)
		bulkInsertQuery = bulkInsertQuery + `{"index": {"_id": "` + incidentDocID + `"}}` + "\n"
		bulkInsertQuery += string(incidentBytes) + "\n"

		currentCount++

		if currentCount > maxRecords {
			if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
				return err
			}

			logger.Print(logger.INFO, "Cloud Incident bulk API Successful for Azure Defender SecurityAlerts for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
			currentCount = 0
			bulkInsertQuery = ""
		}
	}

	if currentCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
			return err
		}

		logger.Print(logger.INFO, "Cloud Incident bulk API Successful for Azure Defender SecurityAlerts for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
	}

	return nil
}
