package okta

import (
	"context"
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/okta/okta-sdk-golang/v2/okta"
	"github.com/okta/okta-sdk-golang/v2/okta/query"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func fetchAppsOfUser(client *okta.Client, userID, tenantID, envID string) (userApps []common.IDPUserApp, err error) {

	listUserAppsParam := query.NewQueryParams(
		query.WithFilter("(user.id eq \""+userID+"\")"),
		query.WithExpand("user/"+userID),
		query.WithLimit(200),
	)

	apps, appsResp, err := client.Application.ListApplications(context.TODO(), listUserAppsParam)
	if err != nil {
		logger.Print(logger.ERROR, "Got error getting apps of user", []string{tenantID}, err)
		return
	}

	for _, app := range apps {

		if app.IsApplicationInstance() {

			if application, ok := app.(*okta.Application); ok {

				var (
					appUserEmbedded AppUserEmbedded
					appActive       bool
				)

				marshaledAppUser, err := json.Marshal(application.Embedded)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
					continue
				}

				if err = json.Unmarshal(marshaledAppUser, &appUserEmbedded); err != nil {
					logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
					continue
				}

				appUserAdditionalData := map[string]any{
					"samlRoles": appUserEmbedded.User.Profile.SamlRoles,
				}

				appUserAdditionalDataJson, err := json.Marshal(appUserAdditionalData)
				if err != nil {
					logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
					continue
				}

				if application.Status == "ACTIVE" {
					appActive = true
				}

				userApps = append(userApps, common.IDPUserApp{
					AppID:             application.Id,
					AppName:           application.Name,
					AppLabel:          application.Label,
					AppUsername:       appUserEmbedded.User.Credentials.Username,
					AppUserAdditional: string(appUserAdditionalDataJson),
					Active:            appActive,
				})
			}
		}
	}

	for {

		if appsResp.HasNextPage() {

			if rateLimitReached(appsResp, client, tenantID, envID) {
				logger.Print(logger.INFO, "Okta apps of user api rate limit has reached 50 percent. Waiting for 1 minute ", []string{tenantID})
				time.Sleep(1 * time.Minute)
			}

			var paginatedApps []*okta.App
			appsResp, err = appsResp.Next(context.TODO(), &paginatedApps)
			if err != nil {
				logger.Print(logger.ERROR, "Got error getting apps", []string{tenantID}, err)
				break
			}

			for _, app := range paginatedApps {

				if (*app).IsApplicationInstance() {
					if application, ok := (*app).(*okta.Application); ok {

						var (
							appUserEmbedded AppUserEmbedded
							appActive       bool
						)

						marshaledAppUser, err := json.Marshal(application.Embedded)
						if err != nil {
							logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
							continue
						}

						if err = json.Unmarshal(marshaledAppUser, &appUserEmbedded); err != nil {
							logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
							continue
						}

						appUserAdditionalData := map[string]any{
							"samlRoles": appUserEmbedded.User.Profile.SamlRoles,
						}

						appUserAdditionalDataJson, err := json.Marshal(appUserAdditionalData)
						if err != nil {
							logger.Print(logger.ERROR, "Got error marshalling", []string{tenantID}, err)
							continue
						}

						if application.Status == "ACTIVE" {
							appActive = true
						}

						userApps = append(userApps, common.IDPUserApp{
							AppID:             application.Id,
							AppName:           application.Name,
							AppLabel:          application.Label,
							AppUsername:       appUserEmbedded.User.Credentials.Username,
							AppUserAdditional: string(appUserAdditionalDataJson),
							Active:            appActive,
						})
					}
				}
			}
		} else {
			break
		}
	}

	return
}

func fetchGroupsOfUser(client *okta.Client, userID, tenantID, envID string) (groupIDs []string, err error) {

	groups, groupsResp, err := client.User.ListUserGroups(context.TODO(), userID)
	if err != nil {
		logger.Print(logger.ERROR, "Got error getting groups of user", []string{tenantID}, err)
		return
	}

	for _, group := range groups {
		groupIDs = append(groupIDs, group.Id)
	}

	for {

		if groupsResp.HasNextPage() {

			if rateLimitReached(groupsResp, client, tenantID, envID) {
				logger.Print(logger.INFO, "Okta groups of user api rate limit has reached 50 percent. Waiting for 1 minute ", []string{tenantID})
				time.Sleep(1 * time.Minute)
			}

			for _, group := range groups {
				groupIDs = append(groupIDs, group.Id)
			}
		} else {
			break
		}
	}

	return
}

func insertUserAndIdentityDoc(user *okta.User, apps []common.IDPUserApp, groups []string, tenantID, domain string, insertTime time.Time, bulkUpdateQuery *string, currentCount *int) (err error) {

	var additionalDetails = make(map[string]string)

	if user.Profile != nil {

		for k, v := range *user.Profile {

			if val, ok := v.(string); ok {
				if k == "login" || k == "email" || k == "secondEmail" || k == "managerId" {
					val = strings.ToLower(val)
				}
				additionalDetails[k] = val
			}
		}
	}

	loginEmail := additionalDetails["login"]     // username in okta UI
	secondaryEmail := additionalDetails["email"] // primaryEmail in okta UI

	name := additionalDetails["firstName"]

	if len(additionalDetails["middleName"]) > 0 {
		name += " " + additionalDetails["middleName"]
	}

	if len(additionalDetails["lastName"]) > 0 {
		name += " " + additionalDetails["lastName"]
	}

	var deleted bool

	if user.Status == "DEPROVISIONED" || user.Status == "DELETED" {
		deleted = true
	}

	if addr, err := common.ParseAddress(loginEmail); err == nil {
		loginEmail = addr.Address
	}

	idpUser := common.IDPUsersDoc{
		UserID:          user.Id,
		Email:           loginEmail,
		SecondaryEmail:  secondaryEmail,
		Name:            name,
		Title:           additionalDetails["title"],
		ManagerID:       additionalDetails["managerId"],
		ManagerName:     additionalDetails["manager"],
		CreatedTime:     elastic.DateTime(*user.Created),
		LastUpdatedTime: elastic.DateTime(*user.LastUpdated),
		IDPType:         common.OKTA_IDP_TYPE,
		Deleted:         deleted,
		UserType:        additionalDetails["userType"],
		Organization:    additionalDetails["organization"],
		Department:      additionalDetails["department"],
		Division:        additionalDetails["division"],
		Status:          user.Status,
		Apps:            apps,
		Groups:          groups,
		Tenantid:        tenantID,
		Domain:          domain,
		InsertTime:      elastic.DateTime(insertTime),
	}

	if user.LastLogin != nil {
		idpUser.LastLoginTime = elastic.DateTime(*user.LastLogin)
	}

	if _, err = elastic.InsertDocument(tenantID, elastic.IDP_USERS_INDEX, idpUser, common.GenerateCombinedHashID(user.Id, domain, tenantID)); err != nil {
		return
	}

	var identityID string

	if addr, err := common.ParseAddress(loginEmail); err == nil {
		identityID = addr.Address
	} else {
		identityID = user.Id
	}

	var identityAppsAccess []common.IdentityApplicationAccess

	for _, app := range apps {
		if app.Active {
			identityAppsAccess = append(identityAppsAccess, common.IdentityApplicationAccess{
				Name:     app.AppName,
				Label:    app.AppLabel,
				Username: app.AppUsername,
			})
		}
	}

	appAccess, err := json.Marshal(identityAppsAccess)
	if err != nil {
		return
	}

	additionalDetails["appAccess"] = string(appAccess)
	docID := common.GenerateCombinedHashIDCaseSensitive(tenantID, "okta", identityID, domain, common.OKTA_USER_IDENTITY_TYPE)

	insertDoc := common.IdentitiesDoc{
		IdentityID:        identityID,
		Name:              name,
		ServiceCode:       "okta",
		AdditionalDetails: additionalDetails,
		IsCrossAccount:    false,
		Permission:        -1,
		AccountType:       0,
		Type:              common.OKTA_USER_IDENTITY_TYPE,
		AccountID:         domain,
		CreatedDate:       elastic.DateTime(*user.Created),
		UpdatedDate:       elastic.DateTime(insertTime),
		Deleted:           deleted,
		TenantID:          tenantID,
		ID:                docID,
		IdentityStatus:    common.UNKNOWN_IDENTITY_STATUS,
	}

	updateDoc := map[string]any{
		"name":              name,
		"exUser":            deleted,
		"additionalDetails": additionalDetails,
		"updatedDate":       elastic.DateTime(insertTime),
		"deleted":           deleted,
	}

	if user.LastLogin != nil {
		insertDoc.LastReadTime = (*user.LastLogin).UnixMilli()
	} else {
		insertDoc.LastReadTime = 0
	}

	upsertRequest := map[string]any{
		"doc":    updateDoc,
		"upsert": insertDoc,
	}

	updateMetadataJSON, err := json.Marshal(upsertRequest)
	if err != nil {
		logger.Print(logger.ERROR, "Error marshalling update metadata", err)
		return
	}

	identitiesUpdateMetadata := `{"update": {"_id": "` + docID + `"}}`
	*bulkUpdateQuery = *bulkUpdateQuery + identitiesUpdateMetadata + "\n" + string(updateMetadataJSON) + "\n"
	*currentCount++

	if *currentCount > MAX_RECORDS {
		if err = elastic.BulkDocumentsAPI(tenantID, elastic.IDENTITIES_INDEX, *bulkUpdateQuery); err != nil {
			return
		}

		logger.Print(logger.INFO, "Identities bulk API Successful for okta users for "+strconv.Itoa(*currentCount)+" records", []string{tenantID})
		*currentCount = 0
		*bulkUpdateQuery = ""
	}

	return
}
