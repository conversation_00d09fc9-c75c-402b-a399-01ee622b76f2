package orca

import (
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
	"github.com/precize/provider/tenant"
	"github.com/precize/transport"
)

const (
	ORCA_URL    = "https://api.orcasecurity.io/api/"
	LIMIT       = "1000"
	bufferTime  = 24 * time.Hour
	MAX_RECORDS = 5000
)

func ValidateAuth(orcaEnv tenant.OrcaEnvironment, tenantID string) (authValidation map[string]bool, err error) {

	authValidation = make(map[string]bool)

	header := make(map[string]string)
	urlParams := make(map[string]string)

	header["Authorization"] = "Token " + orcaEnv.Token

	// urlParams["dsl_filter"] = filter
	urlParams["limit"] = "1"

	if _, err = transport.SendRequest("GET", ORCA_URL+"query/alerts", urlParams, header, nil); err != nil {
		logger.Print(logger.ERROR, "Error Orca API", []string{tenantID}, urlParams, header)
		authValidation[orcaEnv.Token] = false
		return
	}

	authValidation[orcaEnv.Token] = true
	return
}

func ProcessOrcaAlerts(tenantID string, csp string, orcaEnv tenant.OrcaEnvironment, orcaAlertStartTime, orcaAlertEndTime, tenantStartTime time.Time) {

	if orcaAlertEndTime.Sub(orcaAlertStartTime) < bufferTime {
		return
	}

	defaultTime := tenant.FetchAttributeDefaultTime(tenant.ORCA_ALERT, tenantStartTime)

	if (orcaAlertEndTime.Sub(orcaAlertStartTime)) > orcaAlertEndTime.Sub(defaultTime) {
		orcaAlertStartTime = defaultTime
	}

	header := make(map[string]string)
	if len(orcaEnv.Token) > 0 {
		header["Authorization"] = "Token " + orcaEnv.Token
	} else {
		return
	}

	logger.Print(logger.INFO, "Fetching Orca Alerts for "+csp+" from "+common.DateTime(orcaAlertStartTime)+" to "+common.DateTime(orcaAlertEndTime), []string{tenantID})

	var (
		nextPageToken   string
		urlParams       = make(map[string]string)
		bulkInsertQuery string
		currentCount    int
		maxRecords      = 1000
	)

	filter :=
		`{"filter": [{"field": "state.last_updated","range": {"gt": "` + elastic.DateTime(orcaAlertStartTime) + `","lte": "` + elastic.DateTime(orcaAlertEndTime) + `"}},{"field": "cloud_provider","includes": "` + csp + `"}]}`

	for {

		urlParams["dsl_filter"] = filter

		urlParams["limit"] = LIMIT
		if len(nextPageToken) > 0 {
			urlParams["next_page_token"] = nextPageToken
		}

		response, err := transport.SendRequest("GET", ORCA_URL+"query/alerts", urlParams, header, nil)
		if err != nil {
			logger.Print(logger.ERROR, "Error Orca API", []string{tenantID}, urlParams, header)
			break
		}

		var alertData map[string]any
		err = json.Unmarshal(response, &alertData)
		if err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", []string{tenantID}, err)
			break
		}

		err = processOrcaAlerts(alertData, tenantID, csp, orcaAlertEndTime, &bulkInsertQuery, &currentCount)
		if err != nil {
			break
		}

		if currentCount > maxRecords {

			if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
				logger.Print(logger.ERROR, "Error in bulk insertion of cloud incidents", []string{tenantID}, err)
				break
			}

			logger.Print(logger.INFO, "Cloud Incident bulk API Successful for Orca Alerts for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
			currentCount = 0
			bulkInsertQuery = ""
		}

		if hasToken, ok := alertData["has_next_page_token"].(bool); ok && hasToken {
			if token, ok := alertData["next_page_token"].(string); ok {
				nextPageToken = token
			}

			time.Sleep(1 * time.Second)
		} else {
			break
		}
	}

	if currentCount > 0 {
		if err := elastic.BulkDocumentsAPI(tenantID, elastic.CLOUD_INCIDENTS_INDEX, bulkInsertQuery); err != nil {
			logger.Print(logger.ERROR, "Error in bulk insertion of cloud incidents", []string{tenantID}, err)
			return
		}

		logger.Print(logger.INFO, "Cloud Incident bulk API Successful for Orca Alerts for "+strconv.Itoa(currentCount)+" records", []string{tenantID})
	}

	tenant.CheckAndUpdateLastFetchTimeForTenant(tenantID, tenant.ORCA_ALERT, orcaAlertEndTime)

}

func processOrcaAlerts(aData map[string]any, tenantID, csp string, insertionTime time.Time, bulkInsertQuery *string, currentCount *int) error {

	if alertsData, ok := aData["data"].([]any); ok {

		for _, alertData := range alertsData {

			alertDataBytes, err := json.Marshal(alertData)
			if err != nil {
				logger.Print(logger.ERROR, "Error marshalling alert data to JSON:", []string{tenantID}, err)
				continue
			}

			var alertsData common.OrcaAlert
			err = json.Unmarshal(alertDataBytes, &alertsData)
			if err != nil {
				logger.Print(logger.ERROR, "Error unmarshalling JSON:", []string{tenantID}, err)
				continue
			}

			incidentDocID := common.GenerateCombinedHashID(tenantID, alertsData.State.AlertID)

			issue := alertsData.Data.Title

			if len(issue) <= 0 {
				issue = alertsData.Description
			}

			createdAt, err := time.Parse(elastic.ORCA_DATE_FORMAT, alertsData.State.CreatedAt)
			if err != nil {
				logger.Print(logger.ERROR, "Error converting time", []string{tenantID}, createdAt)
			}

			updatedAt, err := time.Parse(elastic.ORCA_DATE_FORMAT, alertsData.State.LastUpdated)
			if err != nil {
				logger.Print(logger.ERROR, "Error converting time", []string{tenantID}, updatedAt)
			}

			if serviceId, ok := common.CspStrToIdIntMap[csp]; ok {

				recommendationFindings := make([]common.RecommendationFindings, 0)

				for _, findingsData := range alertsData.Findings.CVE {

					if len(findingsData.ID) != 0 {

						var recommendationFinding common.RecommendationFindings

						description := findingsData.Description
						if len(findingsData.Description) > 0 {
							sentences := strings.Split(findingsData.Description, ". ")

							if len(sentences) >= 3 {
								sentences = (sentences[:3])
							}

							description = strings.Join(sentences, ". ") + "."
						}

						var recommendationScore float64
						switch v := findingsData.Score.(type) {
						case float64:
							recommendationScore = v
						case string:
							f, err := strconv.ParseFloat(v, 64)
							if err == nil {
								recommendationScore = f
							}
						}

						recommendationFinding.Description = description
						recommendationFinding.ID = findingsData.ID
						recommendationFinding.Impact = findingsData.Impact
						recommendationFinding.Score = float32(recommendationScore)
						recommendationFinding.Severity = findingsData.Severity

						recommendationFindings = append(recommendationFindings, recommendationFinding)
					}
				}

				additionalInfo := common.OrcaAdditionalInfo{
					IsInternetFacing:       alertsData.Model.Data.Inventory.IsInternetFacing,
					RecommendationFindings: recommendationFindings,
				}

				additionalInfoJson, err := json.Marshal(additionalInfo)
				if err != nil {
					logger.Print(logger.ERROR, "Error Marshalling alert json", err)
					continue
				}

				additionalInfoString := string(additionalInfoJson)

				alertsData.Findings = common.Findings{}

				jsonData, err := json.Marshal(alertsData)
				if err != nil {
					logger.Print(logger.ERROR, "Error Marshalling alert json", err)
					continue
				}

				jsonString := string(jsonData)

				entityType := alertsData.AssetType

				if precizeResourceType, ok := common.OrcaToEntityTypeMappings[alertsData.AssetType]; ok {
					entityType = precizeResourceType
				}

				severity := mapOrcaSeverity(alertsData.State.Severity)
				status := common.INCIDENT_STATUS_OPEN
				if len(alertsData.State.ClosedReason) > 0 || strings.Contains(strings.ToLower(alertsData.State.Status), "clos") {
					status = common.INCIDENT_STATUS_RESOLVED
				}

				var isIncident = false

				if alertsData.Category == "Malicious activity" || alertsData.Category == "Suspicious activity" || alertsData.Category == "Malware" {
					isIncident = true
				}

				var scoreFloat float64
				switch v := alertsData.State.OrcaScore.(type) {
				case float64:
					scoreFloat = v
				case string:
					f, err := strconv.ParseFloat(v, 64)
					if err == nil {
						scoreFloat = f
					}
				}

				orcaScore := float32(scoreFloat)

				incident := common.Incident{
					ID:                 incidentDocID,
					AlertID:            alertsData.State.AlertID,
					Issue:              issue,
					EntityID:           alertsData.AssetName,
					EntityType:         entityType,
					Source:             common.ORCA_SOURCE,
					IssueSeverityScore: orcaScore,
					IssueSeverity:      alertsData.State.RiskLevel, // High, Medium, Low
					SourceRisk:         severity,                   // Imminent Compromise, Hazardous
					CreatedAt:          elastic.DateTime(createdAt),
					UpdatedAt:          elastic.DateTime(updatedAt),
					ServiceID:          serviceId,
					Category:           alertsData.Category,
					Description:        alertsData.Details,
					Status:             status,
					Stage:              "dc",
					TenantID:           tenantID,
					SourceJson:         jsonString,
					InsertTime:         elastic.DateTime(insertionTime),
					AdditionalData:     additionalInfoString,
					AccountName:        alertsData.AccountName,
					IsIncident:         isIncident,
				}

				if doc, _ := elastic.GetDocument(elastic.CLOUD_INCIDENTS_INDEX, incidentDocID); len(doc) > 0 {

					docBytes, err := json.Marshal(doc)
					if err != nil {
						logger.Print(logger.ERROR, "Error marshalling", []string{tenantID}, err)
						continue
					}

					var existingIncident common.Incident
					if err = json.Unmarshal(docBytes, &existingIncident); err != nil {
						logger.Print(logger.ERROR, "Error unmarshalling", []string{tenantID}, err)
						continue
					}

					incident.EntityID = existingIncident.EntityID
					incident.AccountID = existingIncident.AccountID
					incident.CrsID = existingIncident.CrsID
				} else {

					if incident.EntityType == "ContainerImage" {

						projectName, repoName, version := GetResourceDetailsForContainerImage(incident.EntityID)
						if projectName != "" && repoName != "" && version != "" {
							entityID, err := GetPrecizeEntityIDForContainerImage(projectName, repoName, version, incident.EntityType, tenantID)
							if err != nil {
								logger.Print(logger.ERROR, "Error fetching cloud resources for container image", []string{tenantID}, incident.EntityID, err)
								continue
							}

							if len(entityID) > 0 {
								incident.EntityID = entityID
							}
						}
					}

					crs, err := common.GetCRSDocFromNames(incident.EntityID, incident.EntityType, incident.AccountName, tenantID)
					if err != nil {
						continue
					}

					if crs.ID != "" {

						incident.CrsID = crs.ID
						incident.EntityID = crs.EntityID
						incident.ResourceName = crs.ResourceName
						incident.AccountID = crs.AccountID
						incident.Environment = crs.Environment
						incident.Owner = crs.Owner
						incident.RelatedResources = crs.RelatedResources
					}
				}

				incidentBytes, _ := json.Marshal(incident)
				*bulkInsertQuery = *bulkInsertQuery + `{"index": {"_id": "` + incidentDocID + `"}}` + "\n"
				*bulkInsertQuery += string(incidentBytes) + "\n"

				*currentCount++

			}
		}
	}

	logger.Print(logger.INFO, "Orca Alert Processed for "+strconv.Itoa(*currentCount)+" records", []string{tenantID})

	return nil
}

func mapOrcaSeverity(orcaSeverity string) (severity string) {

	switch orcaSeverity {
	case "imminent compromise":
		severity = common.CRITICAL_RISK
	case "hazardous":
		severity = common.HIGH_RISK
	default:
		severity = common.MEDIUM_RISK
	}

	return
}
