package orca

const (
	// all values should be in lower case
	GKE_RESOURCE_TYPE                            = "gke"
	VM_RESOURCE_TYPE                             = "vm"
	APIKEY_RESOURCE_TYPE                         = "gcpapikey"
	CLOUDACCOUNT_RESOURCE_TYPE                   = "cloudaccount"
	GCP_BIGQUERYDATASET_RESOURCE_TYPE            = "gcpbigquerydataset"
	GCP_BIGTABLECLUSTER_RESOURCE_TYPE            = "gcpbigtablecluster"
	GCP_BIGTABLEINSTANCE_RESOURCE_TYPE           = "gcpbigtableinstance"
	GCP_SECRETMANAGERSECRET_RESOURCE_TYPE        = "gcpsecretmanagersecret"
	GCP_SPANNERINSTANCE_RESOURCE_TYPE            = "gcpspannerinstance"
	GCP_STORAGEBUCKET_RESOURCE_TYPE              = "storage"
	GCP_VPNGATEWAY_RESOURCE_TYPE                 = "gcpvpngateway"
	IP_RESOURCE_TYPE                             = "newip"
	GCP_SCHEDULESNAPSHOT_RESOURCE_TYPE           = "gcpschedulesnapshot"
	GCP_CLOUDRUNSERVICE_RESOURCE_TYPE            = "gcpcloudrunservice"
	GCP_SQLINSTANCE_RESOURCE_TYPE                = "gcpsqlinstance"
	GCP_VPNTUNNEL_RESOURCE_TYPE                  = "gcpvpntunnel"
	GCP_SECRETMANAGERSECRETVERSION_RESOURCE_TYPE = "gcpsecretmanagersecretversion"
	GCP_COMPUTETARGETPOOL_RESOURCE_TYPE          = "gcpcomputetargetpool"
	GCP_FILESTOREINSTANCE_RESOURCE_TYPE          = "gcpfilestoreinstance"
	GCP_LOADBALANCERBACKENDSERVICE_RESOURCE_TYPE = "gcploadbalancerbackendservice"
	FUNCTION_RESOURCE_TYPE                       = "function"
	GCP_CERTIFICATE_RESOURCE_TYPE                = "gcpcertificate"
	GCP_DNSMANAGEDZONE_RESOURCE_TYPE             = "gcpdnsmanagedzone"
	GCP_VPCFIREWALLRULE_RESOURCE_TYPE            = "gcpvpcfirewallrule"
	GCP_IMAGE_RESOURCE_TYPE                      = "vmimage"
	GCP_REDISINSTANCE_RESOURCE_TYPE              = "gcpredisinstance"
	GCP_KMSKEY_RESOURCE_TYPE                     = "gcpkmskey"
	GCP_LOADBALANCER_RESOURCE_TYPE               = "gcploadbalancer"
	GCP_MEMCACHECLUSTER_RESOURCE_TYPE            = "gcpmemcachecluster"
	GCP_CONTAINERIMAGE_RESOURCE_TYPE             = "containerimage"
	GCP_LOADBALANCERFORWARDINGRULE_RESOURCE_TYPE = "gcploadbalancerforwardingrule"
	GCP_BIGQUERYTABLE_RESOURCE_TYPE              = "gcpbigquerytable"
	GCP_GKECLUSTER_RESOURCE_TYPE                 = "gcpgkecluster"
	GCP_SPANNERDATABASE_RESOURCE_TYPE            = "gcpspannerdatabase"
	VMCONTAINER_RESOURCE_TYPE                    = "container"
)
