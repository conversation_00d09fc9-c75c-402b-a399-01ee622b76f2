2025/07/08 16:06:04 Application config could not be read. Starting with defaults - application.yml - 
2025/07/08 16:06:04 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/07/08 16:06:04 Index iac_git_commits exists - 
2025/07/08 16:06:04 Index cfstack_templates exists - 
2025/07/08 16:06:04 Index arm_templates exists - 
2025/07/08 16:06:04 Index terraform_resources exists - 
2025/07/08 16:06:04 Index tf_commits exists - 
2025/07/08 16:06:04 Index tf_variables exists - 
2025/07/08 16:06:04 Index resource_context exists - 
2025/07/08 16:06:04 Index text_lookup exists - 
2025/07/08 16:06:05 Index ai_resources exists - 
2025/07/08 16:06:05 Index idp_events exists - 
2025/07/08 16:06:05 Index idp_users exists - 
2025/07/08 16:06:05 Index idp_apps exists - 
2025/07/08 16:06:05 Index idp_groups exists - 
2025/07/08 16:06:05 Index cloud_incidents exists - 
2025/07/08 16:06:05 Index jira_issues exists - 
2025/07/08 16:06:05 Index jira_data exists - 
2025/07/08 16:06:05 Index jira_resources exists - 
2025/07/08 16:06:05 Index precize_creations exists - 
2025/07/08 16:06:05 Index external_cloud_resources exists - 
2025/07/08 16:06:05 Initializing tenant providers - 
2025/07/08 16:06:07 Initialized tenant providers - 
2025/07/08 16:06:07 Initializing generic constants - 
2025/07/08 16:06:07 Initialized generic constants - 
2025/07/08 16:06:07 Starting pre-commit cronjob - 
2025/07/08 16:06:07 Finished pre-commit cronjob - 
2025/07/08 16:06:07 [6] [0R8Da4gBoELr5xpoQ6Y3] Processing tenant - 
2025/07/08 16:06:07 [6] Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/07/08 16:06:33 [6] [0R8Da4gBoELr5xpoQ6Y3][124474729313] Fetching Security hub findings from 2024-09-13T17:25:14.675Z to 2025-07-07T10:36:16.039Z - 
2025/07/08 16:08:25 Application config could not be read. Starting with defaults - application.yml - 
2025/07/08 16:08:25 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/07/08 16:08:25 Index iac_git_commits exists - 
2025/07/08 16:08:25 Index cfstack_templates exists - 
2025/07/08 16:08:25 Index arm_templates exists - 
2025/07/08 16:08:25 Index terraform_resources exists - 
2025/07/08 16:08:25 Index tf_commits exists - 
2025/07/08 16:08:25 Index tf_variables exists - 
2025/07/08 16:08:25 Index resource_context exists - 
2025/07/08 16:08:25 Index text_lookup exists - 
2025/07/08 16:08:25 Index ai_resources exists - 
2025/07/08 16:08:25 Index idp_events exists - 
2025/07/08 16:08:25 Index idp_users exists - 
2025/07/08 16:08:25 Index idp_apps exists - 
2025/07/08 16:08:25 Index idp_groups exists - 
2025/07/08 16:08:26 Index cloud_incidents exists - 
2025/07/08 16:08:26 Index jira_issues exists - 
2025/07/08 16:08:26 Index jira_data exists - 
2025/07/08 16:08:26 Index jira_resources exists - 
2025/07/08 16:08:26 Index precize_creations exists - 
2025/07/08 16:08:26 Index external_cloud_resources exists - 
2025/07/08 16:08:26 Initializing tenant providers - 
2025/07/08 16:08:27 Initialized tenant providers - 
2025/07/08 16:08:27 Initializing generic constants - 
2025/07/08 16:08:27 Initialized generic constants - 
2025/07/08 16:08:27 Starting pre-commit cronjob - 
2025/07/08 16:08:27 Finished pre-commit cronjob - 
2025/07/08 16:08:27 [43] [0R8Da4gBoELr5xpoQ6Y3] Processing tenant - 
2025/07/08 16:08:27 [43] Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/07/08 16:08:41 [43] [0R8Da4gBoELr5xpoQ6Y3][124474729313] Fetching Security hub findings from 2024-09-13T17:25:14.675Z to 2025-07-07T10:38:35.896Z - 
2025/07/08 16:10:15 Application config could not be read. Starting with defaults - application.yml - 
2025/07/08 16:10:15 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/07/08 16:10:16 Index iac_git_commits exists - 
2025/07/08 16:10:16 Index cfstack_templates exists - 
2025/07/08 16:10:16 Index arm_templates exists - 
2025/07/08 16:10:16 Index terraform_resources exists - 
2025/07/08 16:10:16 Index tf_commits exists - 
2025/07/08 16:10:16 Index tf_variables exists - 
2025/07/08 16:10:16 Index resource_context exists - 
2025/07/08 16:10:16 Index text_lookup exists - 
2025/07/08 16:10:16 Index ai_resources exists - 
2025/07/08 16:10:16 Index idp_events exists - 
2025/07/08 16:10:16 Index idp_users exists - 
2025/07/08 16:10:16 Index idp_apps exists - 
2025/07/08 16:10:16 Index idp_groups exists - 
2025/07/08 16:10:16 Index cloud_incidents exists - 
2025/07/08 16:10:16 Index jira_issues exists - 
2025/07/08 16:10:16 Index jira_data exists - 
2025/07/08 16:10:16 Index jira_resources exists - 
2025/07/08 16:10:16 Index precize_creations exists - 
2025/07/08 16:10:16 Index external_cloud_resources exists - 
2025/07/08 16:10:16 Initializing tenant providers - 
2025/07/08 16:10:17 Initialized tenant providers - 
2025/07/08 16:10:17 Initializing generic constants - 
2025/07/08 16:10:18 Initialized generic constants - 
2025/07/08 16:10:18 Starting pre-commit cronjob - 
2025/07/08 16:10:18 Finished pre-commit cronjob - 
2025/07/08 16:10:18 [52] [0R8Da4gBoELr5xpoQ6Y3] Processing tenant - 
2025/07/08 16:10:18 [52] Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/07/08 16:10:52 Application config could not be read. Starting with defaults - application.yml - 
2025/07/08 16:10:52 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/07/08 16:10:52 Index iac_git_commits exists - 
2025/07/08 16:10:53 Index cfstack_templates exists - 
2025/07/08 16:10:53 Index arm_templates exists - 
2025/07/08 16:10:53 Index terraform_resources exists - 
2025/07/08 16:10:53 Index tf_commits exists - 
2025/07/08 16:10:53 Index tf_variables exists - 
2025/07/08 16:10:53 Index resource_context exists - 
2025/07/08 16:10:53 Index text_lookup exists - 
2025/07/08 16:10:53 Index ai_resources exists - 
2025/07/08 16:10:53 Index idp_events exists - 
2025/07/08 16:10:53 Index idp_users exists - 
2025/07/08 16:10:53 Index idp_apps exists - 
2025/07/08 16:10:53 Index idp_groups exists - 
2025/07/08 16:10:53 Index cloud_incidents exists - 
2025/07/08 16:10:53 Index jira_issues exists - 
2025/07/08 16:10:53 Index jira_data exists - 
2025/07/08 16:10:53 Index jira_resources exists - 
2025/07/08 16:10:53 Index precize_creations exists - 
2025/07/08 16:10:53 Index external_cloud_resources exists - 
2025/07/08 16:10:53 Initializing tenant providers - 
2025/07/08 16:10:54 Initialized tenant providers - 
2025/07/08 16:10:54 Initializing generic constants - 
2025/07/08 16:10:54 Initialized generic constants - 
2025/07/08 16:10:54 Starting pre-commit cronjob - 
2025/07/08 16:10:54 Finished pre-commit cronjob - 
2025/07/08 16:10:54 [50] [0R8Da4gBoELr5xpoQ6Y3] Processing tenant - 
2025/07/08 16:10:54 [50] Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/07/08 16:15:07 Application config could not be read. Starting with defaults - application.yml - 
2025/07/08 16:15:07 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/07/08 16:15:07 Index iac_git_commits exists - 
2025/07/08 16:15:08 Index cfstack_templates exists - 
2025/07/08 16:15:08 Index arm_templates exists - 
2025/07/08 16:15:08 Index terraform_resources exists - 
2025/07/08 16:15:08 Index tf_commits exists - 
2025/07/08 16:15:08 Index tf_variables exists - 
2025/07/08 16:15:08 Index resource_context exists - 
2025/07/08 16:15:08 Index text_lookup exists - 
2025/07/08 16:15:08 Index ai_resources exists - 
2025/07/08 16:15:08 Index idp_events exists - 
2025/07/08 16:15:08 Index idp_users exists - 
2025/07/08 16:15:08 Index idp_apps exists - 
2025/07/08 16:15:08 Index idp_groups exists - 
2025/07/08 16:15:08 Index cloud_incidents exists - 
2025/07/08 16:15:08 Index jira_issues exists - 
2025/07/08 16:15:08 Index jira_data exists - 
2025/07/08 16:15:08 Index jira_resources exists - 
2025/07/08 16:15:08 Index precize_creations exists - 
2025/07/08 16:15:08 Index external_cloud_resources exists - 
2025/07/08 16:15:08 Initializing tenant providers - 
2025/07/08 16:15:09 Initialized tenant providers - 
2025/07/08 16:15:09 Initializing generic constants - 
2025/07/08 16:15:09 Initialized generic constants - 
2025/07/08 16:15:09 Starting pre-commit cronjob - 
2025/07/08 16:15:09 Finished pre-commit cronjob - 
2025/07/08 16:15:09 [9] [0R8Da4gBoELr5xpoQ6Y3] Processing tenant - 
2025/07/08 16:15:09 [9] Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/07/08 16:16:44 Application config could not be read. Starting with defaults - application.yml - 
2025/07/08 16:16:44 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/07/08 16:16:45 Index iac_git_commits exists - 
2025/07/08 16:16:45 Index cfstack_templates exists - 
2025/07/08 16:16:45 Index arm_templates exists - 
2025/07/08 16:16:45 Index terraform_resources exists - 
2025/07/08 16:16:45 Index tf_commits exists - 
2025/07/08 16:16:45 Index tf_variables exists - 
2025/07/08 16:16:45 Index resource_context exists - 
2025/07/08 16:16:45 Index text_lookup exists - 
2025/07/08 16:16:45 Index ai_resources exists - 
2025/07/08 16:16:45 Index idp_events exists - 
2025/07/08 16:16:45 Index idp_users exists - 
2025/07/08 16:16:45 Index idp_apps exists - 
2025/07/08 16:16:45 Index idp_groups exists - 
2025/07/08 16:16:45 Index cloud_incidents exists - 
2025/07/08 16:16:45 Index jira_issues exists - 
2025/07/08 16:16:45 Index jira_data exists - 
2025/07/08 16:16:45 Index jira_resources exists - 
2025/07/08 16:16:45 Index precize_creations exists - 
2025/07/08 16:16:46 Index external_cloud_resources exists - 
2025/07/08 16:16:46 Initializing tenant providers - 
2025/07/08 16:16:46 Initialized tenant providers - 
2025/07/08 16:16:46 Initializing generic constants - 
2025/07/08 16:16:46 Initialized generic constants - 
2025/07/08 16:16:46 Starting pre-commit cronjob - 
2025/07/08 16:16:46 Finished pre-commit cronjob - 
2025/07/08 16:16:47 [6] [0R8Da4gBoELr5xpoQ6Y3] Processing tenant - 
2025/07/08 16:16:47 [6] Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/07/08 16:17:01 [6] [0R8Da4gBoELr5xpoQ6Y3][************] Fetching Security hub findings from 2024-09-13T17:25:14.675Z to 2025-07-07T10:46:55.028Z - 
2025/07/08 16:17:30 Application config could not be read. Starting with defaults - application.yml - 
2025/07/08 16:17:30 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/07/08 16:17:30 Index iac_git_commits exists - 
2025/07/08 16:17:30 Index cfstack_templates exists - 
2025/07/08 16:17:30 Index arm_templates exists - 
2025/07/08 16:17:30 Index terraform_resources exists - 
2025/07/08 16:17:30 Index tf_commits exists - 
2025/07/08 16:17:30 Index tf_variables exists - 
2025/07/08 16:17:30 Index resource_context exists - 
2025/07/08 16:17:30 Index text_lookup exists - 
2025/07/08 16:17:30 Index ai_resources exists - 
2025/07/08 16:17:30 Index idp_events exists - 
2025/07/08 16:17:30 Index idp_users exists - 
2025/07/08 16:17:31 Index idp_apps exists - 
2025/07/08 16:17:31 Index idp_groups exists - 
2025/07/08 16:17:31 Index cloud_incidents exists - 
2025/07/08 16:17:31 Index jira_issues exists - 
2025/07/08 16:17:31 Index jira_data exists - 
2025/07/08 16:17:31 Index jira_resources exists - 
2025/07/08 16:17:31 Index precize_creations exists - 
2025/07/08 16:17:31 Index external_cloud_resources exists - 
2025/07/08 16:17:31 Initializing tenant providers - 
2025/07/08 16:17:32 Initialized tenant providers - 
2025/07/08 16:17:32 Initializing generic constants - 
2025/07/08 16:17:32 Initialized generic constants - 
2025/07/08 16:17:32 Starting pre-commit cronjob - 
2025/07/08 16:17:32 Finished pre-commit cronjob - 
2025/07/08 16:17:32 [6] [0R8Da4gBoELr5xpoQ6Y3] Processing tenant - 
2025/07/08 16:17:32 [6] Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/07/08 16:17:42 [6] [0R8Da4gBoELr5xpoQ6Y3][************] Fetching Security hub findings from 2024-09-13T17:25:14.675Z to 2025-07-07T10:47:40.858Z - 
2025/07/08 16:18:28 Application config could not be read. Starting with defaults - application.yml - 
2025/07/08 16:18:28 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/07/08 16:18:28 Index iac_git_commits exists - 
2025/07/08 16:18:28 Index cfstack_templates exists - 
2025/07/08 16:18:28 Index arm_templates exists - 
2025/07/08 16:18:28 Index terraform_resources exists - 
2025/07/08 16:18:28 Index tf_commits exists - 
2025/07/08 16:18:29 Index tf_variables exists - 
2025/07/08 16:18:29 Index resource_context exists - 
2025/07/08 16:18:29 Index text_lookup exists - 
2025/07/08 16:18:29 Index ai_resources exists - 
2025/07/08 16:18:29 Index idp_events exists - 
2025/07/08 16:18:29 Index idp_users exists - 
2025/07/08 16:18:29 Index idp_apps exists - 
2025/07/08 16:18:29 Index idp_groups exists - 
2025/07/08 16:18:29 Index cloud_incidents exists - 
2025/07/08 16:18:29 Index jira_issues exists - 
2025/07/08 16:18:29 Index jira_data exists - 
2025/07/08 16:18:29 Index jira_resources exists - 
2025/07/08 16:18:29 Index precize_creations exists - 
2025/07/08 16:18:29 Index external_cloud_resources exists - 
2025/07/08 16:18:29 Initializing tenant providers - 
2025/07/08 16:18:30 Initialized tenant providers - 
2025/07/08 16:18:30 Initializing generic constants - 
2025/07/08 16:18:30 Initialized generic constants - 
2025/07/08 16:18:30 Starting pre-commit cronjob - 
2025/07/08 16:18:30 Finished pre-commit cronjob - 
2025/07/08 16:18:30 [7] [0R8Da4gBoELr5xpoQ6Y3] Processing tenant - 
2025/07/08 16:18:30 [7] Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/07/08 16:18:41 [7] [0R8Da4gBoELr5xpoQ6Y3][************] Fetching Security hub findings from 2024-09-13T17:25:14.675Z to 2025-07-07T10:48:39.418Z - 
2025/07/08 16:23:53 Application config could not be read. Starting with defaults - application.yml - 
2025/07/08 16:23:53 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/07/08 16:23:54 Index iac_git_commits exists - 
2025/07/08 16:23:54 Index cfstack_templates exists - 
2025/07/08 16:23:54 Index arm_templates exists - 
2025/07/08 16:23:54 Index terraform_resources exists - 
2025/07/08 16:23:54 Index tf_commits exists - 
2025/07/08 16:23:54 Index tf_variables exists - 
2025/07/08 16:23:54 Index resource_context exists - 
2025/07/08 16:23:54 Index text_lookup exists - 
2025/07/08 16:23:54 Index ai_resources exists - 
2025/07/08 16:23:54 Index idp_events exists - 
2025/07/08 16:23:54 Index idp_users exists - 
2025/07/08 16:23:54 Index idp_apps exists - 
2025/07/08 16:23:54 Index idp_groups exists - 
2025/07/08 16:23:54 Index cloud_incidents exists - 
2025/07/08 16:23:54 Index jira_issues exists - 
2025/07/08 16:23:54 Index jira_data exists - 
2025/07/08 16:23:54 Index jira_resources exists - 
2025/07/08 16:23:54 Index precize_creations exists - 
2025/07/08 16:23:54 Index external_cloud_resources exists - 
2025/07/08 16:23:54 Initializing tenant providers - 
2025/07/08 16:23:55 Initialized tenant providers - 
2025/07/08 16:23:55 Initializing generic constants - 
2025/07/08 16:23:55 Initialized generic constants - 
2025/07/08 16:23:55 Starting pre-commit cronjob - 
2025/07/08 16:23:55 Finished pre-commit cronjob - 
2025/07/08 16:23:55 [6] [0R8Da4gBoELr5xpoQ6Y3] Processing tenant - 
2025/07/08 16:23:55 [6] Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/07/08 16:24:06 [6] [0R8Da4gBoELr5xpoQ6Y3][************] Fetching Security hub findings from 2024-09-13T17:25:14.675Z to 2025-07-07T10:54:04.277Z - 
